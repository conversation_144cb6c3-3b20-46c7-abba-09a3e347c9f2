import * as Yup from "yup";
import { MedicationFormPayload } from "../interfaces/task-manager.interface";

export const MEDICATION_FORM_FIRST_STEP_VALIDATION_SCHEMA: Partial<MedicationFormPayload> =
  Yup.object({
    medicineName: Yup.string()
      .trim()
      .required("Medication name is required.")
      .min(3, "Medication name should be at least 3 characters.")
      .max(100, "Medication name can be up to 100 characters"),
    activeIngredient: Yup.string()
      .trim()
      .required("Active ingredient is required.")
      .min(3, "Active ingredient should be at least 3 characters.")
      .max(100, "Active ingredient can be up to 100 characters"),
  });

export const MEDICATION_FORM_SECOND_STEP_VALIDATION_SCHEMA: Partial<MedicationFormPayload> =
  Yup.object({
    medicineName: Yup.string()
      .trim()
      .required("Medication name is required.")
      .min(3, "Medication name should be at least 3 characters.")
      .max(100, "Medication name can be up to 100 characters"),
    doses: Yup.array().of(
      Yup.object().shape({
        categoryTypeId: Yup.mixed()
          .required("Medication Form Type is required.")
          .test(
            "is-valid-category",
            "Medication Form Type must be a number or a valid category",
            (value) => {
              if (typeof value === "number") return true;
              if (typeof value === "object" && value !== null) {
                return (
                  typeof value.id === "number" &&
                  typeof value.label === "string" &&
                  typeof value.value === "number"
                );
              }
              return false;
            }
          ),
        strength: Yup.string()
          .required("Strength is required.")
          .matches(
            /^\d+(\.\d{1,2})?$/,
            "Strength must be a valid number with up to 2 decimal places."
          )
          .matches(
            /^\d+(\.\d{1,2})?$/,
            'Strength must be a number, and "." must be placed between two numbers.'
          )
          .test(
            "is-greater-than-zero",
            "Strength must be greater than 0.",
            (value) => parseFloat(value) > 0
          )
          .test(
            "valid-decimal-placement",
            'Invalid decimal placement. Ensure "." is between two numbers.',
            (value) => /^\d+(\.\d{1,2})?$/.test(value)
          ),
        quantity: Yup.string()
          .required("Quantity is required.")
          .matches(
            /^\d+(\.\d{1,2})?$/,
            "Quantity must be a valid number with up to 2 decimal places."
          )
          .matches(
            /^\d+(\.\d{1,2})?$/,
            'Quantity must be a number, and "." must be placed between two numbers.'
          )
          .test(
            "is-greater-than-zero",
            "Quantity must be greater than 0.",
            (value) => parseFloat(value) > 0
          )
          .test(
            "valid-decimal-placement",
            'Invalid decimal placement. Ensure "." is between two numbers.',
            (value) => /^\d+(\.\d{1,2})?$/.test(value)
          ),
      })
    ).required().min(1, "At least one dose is required"),
  });

export const MEDICATION_FORM_THIRD_STEP_VALIDATION_SCHEMA: Partial<MedicationFormPayload> =
  Yup.object({
    frequencyTypeId: Yup.object().required("Repeats is required."),
    reminder: Yup.object().required("Reminder is required."),
    fromDate: Yup.date()
      .required("From date is required.")
      .test(
        "is-before-until",
        "From date cannot be later than Until date.",
        function (value) {
          const { toDate } = this.parent; // Access other fields in the form
          return (
            !toDate || new Date(value).getTime() <= new Date(toDate).getTime()
          );
        }
      ),
    toDate: Yup.string()
      .nullable() // Allows null for optional fields
      .notRequired()
      .test(
        "is-after-from",
        "Until date cannot be earlier than From date.",
        function (value) {
          const { fromDate } = this.parent;
          return (
            !value || new Date(value).getTime() >= new Date(fromDate).getTime()
          );
        }
      ),
  });

export const FORMULA_FORM_FIRST_STEP_VALIDATION_SCHEMA: Partial<MedicationFormPayload> =
  Yup.object({
    formulaName: Yup.string()
      .trim()
      .required("Formula name is required.")
      .min(3, "Formula name should be at least 3 characters.")
      .max(100, "Formula name can be up to 100 characters"),
    quantity: Yup.string()
      .required("Serving Size is required.")
      .matches(
        /^\d+(\.\d{1,2})?$/,
        "Serving Size must be a valid number with up to 2 decimal places."
      )
      .matches(
        /^\d+(\.\d{1,2})?$/,
        'Serving Size must be a number, and "." must be placed between two numbers.'
      )
      .test(
        "is-greater-than-zero",
        "Serving Size must be greater than 0.",
        (value) => parseFloat(value) > 0
      )
      .test(
        "valid-decimal-placement",
        'Invalid decimal placement. Ensure "." is between two numbers.',
        (value) => /^\d+(\.\d{1,2})?$/.test(value)
      )
      .min(0.01, "Serving Size must be greater than 0."),
  });

export const FORMULA_FORM_SECOND_STEP_VALIDATION_SCHEMA: Partial<MedicationFormPayload> =
  Yup.object({
    frequencyTypeId: Yup.object().required("Repeats is required."),
    reminder: Yup.object().required("Reminder is required."),
    fromDate: Yup.date()
      .required("From date is required.")
      .test(
        "is-before-until",
        "From date cannot be later than Until date.",
        function (value) {
          const { toDate } = this.parent; // Access other fields in the form
          return (
            !toDate || new Date(value).getTime() <= new Date(toDate).getTime()
          );
        }
      ),
    toDate: Yup.string()
      .nullable() // Allows null for optional fields
      .notRequired()
      .test(
        "is-after-from",
        "Until date cannot be earlier than From date.",
        function (value) {
          const { fromDate } = this.parent;
          return (
            !value || new Date(value).getTime() >= new Date(fromDate).getTime()
          );
        }
      ),
  });
