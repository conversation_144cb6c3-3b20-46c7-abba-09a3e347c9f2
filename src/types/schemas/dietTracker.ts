export interface SearchFoodItem {
  id: string;
  description: string;
  quantity?: string;
  phe: number;
  protein: number;
  unit?: string;
}

export interface Meals {
  name: string;
  quantity: string;
  phe: string;
  protein?: string;
}

export interface Portions {
  id: number;
  text: string;
}
export interface FoodEntryItem { //food entry
  category: string;
  nutrients: any;
  description: string;
  quantity: number;
  unit: string;
  gram_weight?: number;
  phe: number;
  protein?: number;
  food_id?: string;
  selectedFromList?: boolean;
  editQuantity?: number;
  editUnit: string;
  editGramWeight?: number;
  isFreeFood?: boolean;
}

export interface Portion {
  name: string;
  quantity: number;
  gram_weight: number;
}

export interface FoodEntry {
  [x: string]: any;
  _id: string;
  time: string;
  client_id: string;
  user_id: string;
  description: string;
  items: FoodEntryItem[];
  ingredients: FoodEntryItem[];
}

// Nutrient Interface
export interface Nutrient {
  name: string;
  amount: number;
  unit: string;
}

// Ingredient Interface
interface Ingredient {
  description: string;
  category: string;
  quantity: number;
  unit: string;
  nutrients: Nutrient[];
}

// FoodItem Interface
export interface FoodItem {
  fooditems: any;
  _id: string;
  client_id: string;
  user_id: string;
  description: string;
  original_description: string | null;
  category: string;
  provider: string;
  quantity: number;
  unit: string;
  time: string; // ISO date string
  nutrients: Nutrient[];
  vector: string | null;
  portions: Portion[];
  ingredients: Ingredient[];
  gram_weight: number
}

// Item Interface
interface Item {
  description: string;
  quantity: number;
  unit: string;
  gram_weight: number;
  phe: number;
  protein: number;
  food_id: string;
  isFreeFood?: boolean;
}

export interface ResponseData {
  _id: string;
  time: string; // ISO date string
  client_id: string;
  user_id: string;
  description: string;
  items: Item[];
  fooditems: FoodItem[];
}

// Define the Payload Interface
export interface CreateFoodPayload {
  description: string;
  category: string;
  quantity: number;
  unit: string;
  time: string; // ISO Date String
  nutrients: {
    name: string;
    amount: number;
    unit: string;
  }[];
  ingredients?: Food[]; // Array of Food objects
  portions?: {
    name: string;
    quantity: number;
    gram_weight: number;
  }[];
}

// Define the Food Interface
export interface Food {
  _id: string;
  client_id: string;
  user_id: string;
  description: string;
  original_description?: string | null;
  category: string;
  provider: string;
  quantity: number;
  unit: string;
  time: string; // ISO Date String
  nutrients: {
    name: string;
    amount: number;
    unit: string;
  }[];
  vector?: any | null;
  portions?: {
    name: string;
    quantity: number;
    gram_weight: number;
  }[];
  ingredients?: Food[]; // Nested Food array
  gram_weight: string;
  food_id: number;
}

export interface CreateFoodEntryPayload {
  description: string;
  time: string;
  items: {
    description: string;
    quantity: number;
    unit: string;
    gram_weight: number;
    phe: number;
    protein: number;
    food_id: string; // Correct prop name
  }[];
}


export enum DietStatus {
  IDLE = "idle",
  LOADING = "loading",
  SUCCESS = "success",
  ERROR = "error",
}

export type TimeSlot = "morning" | "afternoon" | "evening";

export interface ISearchFoodItem  {
  description: string;
  id: string;
  unit: string;
  phe: number;
  protein: number;
  isFreeFood?: boolean;
  pheText: string
  quantity: number
}
