import React, { useState, useRef } from "react";
import {
  Camera,
  useCameraDevice,
  useCameraPermission,
} from "react-native-vision-camera";
import { launchImageLibrary } from "react-native-image-picker";
import { View, TouchableOpacity, Image, Alert, StyleSheet } from "react-native";

import styles from "./CustomCamera.styles";
import { SafeScreen } from "@/components/template";
import Icons from "@/theme/assets/images/svgs/icons";
import { CameraTipsCarousel } from "../CameraTipsCarousel/CameraTipsCarousel";
import { useTheme } from "@/theme";
import { useAppDispatch } from "@/store";
import { setOrientation } from "@/store/slices/dietTrackerSlice";

interface CustomCameraProps {
  isVisible: boolean;
  onClose: () => void;
  onPhotoCaptured: (uri: string) => void;
}

const CustomCamera: React.FC<CustomCameraProps> = ({
  isVisible,
  onClose,
  onPhotoCaptured,
}) => {
  const [cameraPosition, setCameraPosition] = useState<"front" | "back">(
    "back"
  );
  const [flash, setFlash] = useState<"on" | "off">("off");
  const [selectedMedia, setSelectedMedia] = useState<string | null>(null);
  const device = useCameraDevice(cameraPosition);
  const { requestPermission } = useCameraPermission();
  const cameraRef = useRef<Camera>(null);
  const { colors } = useTheme();
  const dispatch = useAppDispatch();

  const openMediaPicker = async () => {
    try {
      const result = await launchImageLibrary({
        mediaType: "photo",
        selectionLimit: 1,
      });

      if (result?.assets?.length) {
        const photoUri = result?.assets?.[0]?.uri || null;
        const asset = result.assets[0];
        if (asset?.width && asset?.height) {
          const orientation =
            asset.height > asset.width ? "portrait" : "landscape";
           dispatch(setOrientation(orientation || ""));
        }
        // console.log(result, "result");
        setSelectedMedia(photoUri);
        if (photoUri) {
          onPhotoCaptured(photoUri);
        }
        // onClose();
      } else if (result.didCancel) {
        onClose();
      } else {
        onClose();
        Alert.alert("Error", "Failed to pick an image.");
      }
    } catch (error) {
      // console.error("Error opening media picker:", error);
      Alert.alert("Error", "Failed to open the media picker.");
    }
  };

  const handleCapture = async () => {
    if (cameraRef.current) {
      try {
        const photo = await cameraRef.current.takePhoto();
        dispatch(setOrientation(photo?.orientation || ""));
        if (photo?.uri) {
          onPhotoCaptured(photo?.uri);
        } else if (photo.path) {
          onPhotoCaptured(photo.path);
        } else {
          console.error("Photo capture returned no URI or path.");
        }
        // onClose();
      } catch (error) {
        console.error("Error capturing photo:", error);
      }
    }
  };

  if (!device) {
    return <View style={styles.center} />;
  }

  return (
    <View style={styles.container}>
      {/* Camera View */}
      <Camera
        ref={cameraRef}
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={true}
        photo={true}
        torch={device?.hasTorch && flash === "on" ? "on" : "off"}
      />

      {/* Flash Button */}
      {device?.position === "back" ? (
        <TouchableOpacity
          style={[
            styles.flashButton,
            {
              backgroundColor: flash === "on" ? colors.white : colors.lightGrey,
            },
          ]}
          onPress={() => setFlash(flash === "on" ? "off" : "on")}
        >
          {flash === "on" ? (
            <Icons.FlashOnIcon width={20} height={20} />
          ) : (
            <Icons.FlashOffIcon width={20} height={20} />
          )}
        </TouchableOpacity>
      ) : null}

      {/* Close Button */}
      <TouchableOpacity style={styles.closeButton} onPress={onClose}>
        <Icons.Close width={40} height={40} hasNoCircle />
      </TouchableOpacity>

      <CameraTipsCarousel style={styles.carousel} />

      <Icons.CameraFrame width={325} height={414} style={styles.cameraFrame} />

      {/* Bottom Controls */}
      <View style={styles.bottomControls}>
        {/* Media Picker */}
        <TouchableOpacity style={styles.mediaPicker} onPress={openMediaPicker}>
          {selectedMedia && (
            <Image
              source={{ uri: selectedMedia }}
              style={styles.mediaPreview}
            />
          )}
        </TouchableOpacity>

        {/* Shutter Button */}
        <TouchableOpacity
          style={styles.shutterButton}
          onPress={() => handleCapture()}
        >
          <View style={styles.shutterInner} />
        </TouchableOpacity>

        {/* Camera Toggle */}
        <TouchableOpacity
          style={styles.cameraToggle}
          onPress={() =>
            setCameraPosition(cameraPosition === "back" ? "front" : "back")
          }
        >
          <Icons.Switch width={45} height={45} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default CustomCamera;
