import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Calendar, LocaleConfig } from "react-native-calendars";
import { Modal, View, TouchableOpacity } from "react-native";

import {
  fetchMonthlyProgress,
  selectMonthlyProgress,
} from "@/store/slices/dashboardSlice";
import { store } from "@/store";
import { config } from "@/theme/_config";
import Common from "@/theme/common.style";
import { Typography } from "@/components/atoms";
import getCustomCalendarStyles from "./CustomCalendar.styles";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { useTheme } from "@/theme";

// Configure locale for single-letter weekdays
LocaleConfig.locales["en"] = {
  monthNames: [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ],
  monthNamesShort: [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ],
  dayNames: [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ],
  dayNamesShort: ["S", "M", "T", "W", "T", "F", "S"],
};

// Set the default locale to "en"
LocaleConfig.defaultLocale = "en";

interface CustomCalendarProps {
  isVisible: boolean;
  onClose: () => void;
  onDateSelect: (range: { startDate: string; endDate?: string }) => void;
  isPeriod?: boolean;
  disableModal?: boolean;
  calendarTheme?: object;
  canSelectFuture?: boolean;
  hideClear?: boolean;
  hasPercentages?: boolean;
  initialDate?: string; // New prop for the initial date
}

const CustomCalendar: React.FC<CustomCalendarProps> = ({
  isVisible,
  onClose,
  onDateSelect,
  isPeriod = false,
  disableModal = false,
  hideClear = false,
  calendarTheme = {},
  canSelectFuture = false,
  hasPercentages = false,
  initialDate, // Prop for setting initial date
}) => {
  const dispatch = useDispatch();
  const progressData = useSelector(selectMonthlyProgress); // Fetch progress data from Redux
  const [markedDates, setMarkedDates] = useState({});
  const [selectedRange, setSelectedRange] = useState({
    startDate: "",
    endDate: "",
  });

  const today = new Date();

  const { colors } = useTheme();
  const styles: any = useDynamicStyles(getCustomCalendarStyles);
  const [currentMonth, setCurrentMonth] = useState(dayjs().format("MM"));
  const [currentYear, setCurrentYear] = useState(dayjs().format("YYYY"));
  const [lastSelectedPeriod, setLastSelectedPeriod] = useState<{
    startDate: string;
    endDate: string;
  }>({
    startDate: "",
    endDate: "",
  });

  useEffect(() => {
    if (!isVisible || !initialDate) return;

    const date = dayjs(initialDate);
    const formatted = date.format("YYYY-MM-DD");

    if (isPeriod) {
      const startOfWeek = date.startOf("week");
      const endOfWeek = startOfWeek.add(6, "day");

      const weekDates = generateWeekDates(startOfWeek.format("YYYY-MM-DD"));

      setMarkedDates(weekDates);
      setSelectedRange({
        startDate: startOfWeek.format("YYYY-MM-DD"),
        endDate: endOfWeek.format("YYYY-MM-DD"),
      });
    } else {
      const percentageDates = generatePercentageDates();
      setMarkedDates({
        ...percentageDates,
        [formatted]: {
          selected: true,
          marked: true,
          selectedColor: colors.primary,
        },
      });
      setSelectedRange({
        startDate: formatted,
        endDate: "",
      });
    }
  }, [isVisible, initialDate, isPeriod]);

  // Fetch data whenever month/year changes
  useEffect(() => {
    if (hasPercentages && isVisible) {
      store.dispatch(
        fetchMonthlyProgress({ month: currentMonth, year: currentYear })
      );
    }
  }, [currentMonth, currentYear, hasPercentages, isVisible, dispatch]);

  const handleMonthChange = (months) => {
    if (months.length > 0) {
      const newMonth = dayjs(months[0].dateString).format("MM");
      const newYear = dayjs(months[0].dateString).format("YYYY");

      setCurrentMonth(newMonth);
      setCurrentYear(newYear);
    }
  };

  // Generate percentage-based dates
  const generatePercentageDates = () => {
    const percentageDates = {};

    if (!progressData || !hasPercentages || isPeriod) return percentageDates;

    progressData.forEach((data) => {
      const dateString = dayjs(data.date).format("YYYY-MM-DD");

      percentageDates[dateString] = {
        customStyles: {
          container: {
            width: 32,
            height: 32,
            borderWidth: 2,
            borderColor:
              data.percentage === 100
                ? colors.primary
                : data.percentage === 0
                  ? colors.mediumGray
                  : colors.yellow,
            borderRadius: 16,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor:
              data.percentage === 100
                ? colors.primary
                : data.percentage === 0
                  ? colors.mediumGray
                  : data.percentage > 0 && data.percentage < 100
                    ? colors.yellow
                    : "transparent", // Do nothing if no percentage
          },
          text: {
            color: colors.white,
            fontWeight: "bold",
          },
        },
      };
    });

    return percentageDates;
  };

  const handleDayPress = (day) => {
    const selectedDate = day.dateString;

    if (isPeriod) {
      const startDate = new Date(selectedDate);
      const startOfWeek = new Date(startDate);
      startOfWeek.setDate(startDate.getDate() - startDate.getDay());
      const weekDates = generateWeekDates(
        startOfWeek.toISOString().split("T")[0]
      );

      const newStartDate = startOfWeek.toISOString().split("T")[0];
      const newEndDate = new Date(
        startOfWeek.setDate(startOfWeek.getDate() + 6)
      )
        .toISOString()
        .split("T")[0];

      setMarkedDates(weekDates);
      setSelectedRange({ startDate: newStartDate, endDate: newEndDate });

      // Store last selected period
      setLastSelectedPeriod({ startDate: newStartDate, endDate: newEndDate });
    } else {
      const percentageDates = generatePercentageDates();
      setMarkedDates({
        ...percentageDates,
        [selectedDate]: {
          selected: true,
          marked: true,
          selectedColor: colors.primary,
        },
      });
      setSelectedRange({ startDate: selectedDate, endDate: "" });
    }
  };

  const generateWeekDates = (startDate: string) => {
    const weekDates: any = {};
    const start = new Date(startDate);

    for (let i = 0; i < 7; i++) {
      const current = new Date(start);
      current.setDate(start.getDate() + i);
      const dateString = current.toISOString().split("T")[0];

      if (i === 0) {
        weekDates[dateString] = {
          startingDay: true,
          color: colors.primary,
          textColor: "white",
        };
      } else if (i === 6) {
        weekDates[dateString] = {
          endingDay: true,
          color: colors.primary,
          textColor: "white",
        };
      } else {
        weekDates[dateString] = {
          color: colors.highlight,
          textColor: "white",
        };
      }
    }

    return weekDates;
  };

  const handleConfirm = () => {
    onDateSelect(selectedRange);
    onClose();
  };

  const handleClear = () => {
    setMarkedDates({});
    setSelectedRange({ startDate: "", endDate: "" });
    onDateSelect({ startDate: "", endDate: "" });
    onClose();
  };

  // Handle initial date marking
  useEffect(() => {
    const dateToSelect = initialDate ? dayjs(initialDate) : dayjs(); // Use initialDate if provided, otherwise default to current date
    const formattedDate = dateToSelect.format("YYYY-MM-DD");

    setMarkedDates((prevMarkedDates) => ({
      ...prevMarkedDates,
      [formattedDate]: {
        selected: true,
        marked: true,
        selectedColor: colors.primary,
      },
    }));

    setSelectedRange({
      startDate: formattedDate,
      endDate: "",
    });

    if (isPeriod) {
      const startDate = dayjs().startOf("week"); // Start of the week for the current date
      const endDate = startDate.add(6, "day"); // End of the week

      const weekDates = generateWeekDates(startDate.format("YYYY-MM-DD"));

      setMarkedDates((prevMarkedDates) => ({
        ...prevMarkedDates,
        ...weekDates,
      }));

      setSelectedRange({
        startDate: startDate.format("YYYY-MM-DD"),
        endDate: endDate.format("YYYY-MM-DD"),
      });
    } else {
      const percentageDates = generatePercentageDates();
      setMarkedDates((prevMarkedDates) => ({
        ...prevMarkedDates,
        ...percentageDates,
      }));
    }
  }, [isPeriod, progressData, initialDate]);

  // Calendar Content
  const CalendarContent = () => (
    <View style={styles.calendarContainer}>
      <Calendar
        markingType={isPeriod ? "period" : "custom"}
        markedDates={markedDates}
        onDayPress={handleDayPress}
        onVisibleMonthsChange={handleMonthChange}
        maxDate={
          canSelectFuture ? undefined : today.toISOString().split("T")[0]
        }
        theme={{
          todayTextColor: colors.primary,
          arrowColor: colors.primary,
          textSectionTitleColor: colors.calendarDayText,
          backgroundColor: colors.calendarBg,
          calendarBackground: colors.calendarBg,
          textDisabledColor: colors.calendarDisableText,
          ...calendarTheme,
        }}
        initialDate={
          initialDate
            ? dayjs(initialDate).format("YYYY-MM-DD")
            : dayjs().format("YYYY-MM-DD")
        }
        renderHeader={(date) => {
          const formattedDate = new Date(date).toLocaleDateString("en-US", {
            month: "long",
            year: "numeric",
          });
          return (
            <Typography.H3 style={styles.monthYearHeader}>
              {formattedDate}
            </Typography.H3>
          );
        }}
      />
      <View style={styles.buttonSeparator} />
      <View style={[styles.buttonRow]}>
        {!hideClear ? (
          <TouchableOpacity onPress={handleClear}>
            <Typography.B2 style={[styles.buttonText, Common.textBold]}>
              Clear
            </Typography.B2>
          </TouchableOpacity>
        ) : (
          <View />
        )}
        <View style={styles.rightButtonRow}>
          <TouchableOpacity onPress={onClose} style={{ marginRight: 10 }}>
            <Typography.B2 style={[styles.buttonText, Common.textBold]}>
              Cancel
            </Typography.B2>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleConfirm}>
            <Typography.B2 style={[styles.buttonText, Common.textBold]}>
              OK
            </Typography.B2>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  return disableModal ? (
    CalendarContent()
  ) : (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade" // Changed from "slide" to "fade" for better overlay effect
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>{CalendarContent()}</View>
    </Modal>
  );
};

export default CustomCalendar;
