import React, { FC, ReactNode, useState } from "react";
import { scale } from "react-native-size-matters";
import { Pressable, StyleProp, TextStyle, View, ViewStyle } from "react-native";

import { useTheme } from "@/theme";
import { useAppSelector } from "@/store";
import Typography from "../Typography/Typography";
import Icons from "@/theme/assets/images/svgs/icons";
import { RNCheckboxStyle as styles } from "./RNCheckbox.style";
import { selectIsSimplifiedDiet } from "@/store/slices/settingsSlice";

interface RNCheckboxProps {
  wrapperStyle?: StyleProp<ViewStyle>;
  errorStyle?: StyleProp<ViewStyle>;
  label?: string;
  value: boolean;
  error?: string | boolean | undefined;
  onSelect: (x: any) => void;
  label2?: ReactNode;
  label2Press?: () => void;
  rowStyle?: StyleProp<ViewStyle>;
  lightBorder?: boolean;
  isMeal?: boolean;
  checkedfillColor?: string
  checkboxUnCheckedFillColor?: string
  customLabelText?: StyleProp<TextStyle>
}

type RNCheckboxComponent = FC<RNCheckboxProps> & {
  FreeFood: FC<RNCheckboxProps>;
};

const RNCheckbox: RNCheckboxComponent = ({
  wrapperStyle,
  errorStyle,
  value = false,
  label,
  onSelect,
  error,
  label2,
  label2Press = () => {},
  rowStyle,
  customLabelText,
  ...rest
}) => {
  const { colors } = useTheme();

  function toggleCheckbox() {
    onSelect?.(!value);
  }

  return (
    <View style={[styles.wrapper, wrapperStyle]}>
      <View style={[styles.rowContainer, rowStyle]}>
        <Pressable onPress={toggleCheckbox} style={styles.checkbox}>
          {value ? (
            <Icons.CheckboxChecked color={rest?.checkedfillColor} width={scale(24)} height={scale(24)} />
          ) : rest?.lightBorder ? (
            <Icons.UncheckedCheckbox width={scale(24)} height={scale(24)} />
          ) : (
            <Icons.CheckboxUnChecked color={rest?.checkboxUnCheckedFillColor} width={scale(24)} height={scale(24)} />
          )}
        </Pressable>
        {Boolean(label) && (
          <Typography.B2
            numberOfLines={3}
            style={[
              !Boolean(label2) ? styles.labelStyle2 : styles.labelStyle,
              { color: colors.textPrimary }, customLabelText
            ]}
          >
            {label}
          </Typography.B2>
        )}
        {Boolean(label2) && (
          <Pressable onPress={label2Press}>
            <Typography.B2
              style={[
                styles.labelStyle,
                { color: colors.textPrimary, textDecorationLine: "underline" },
              ]}
              numberOfLines={1}
            >
              {label2}
            </Typography.B2>
          </Pressable>
        )}
      </View>

      <View></View>

      {!!error && (
        <Typography.B4 style={[styles.errorMessage, errorStyle]}>
          {error}
        </Typography.B4>
      )}
    </View>
  );
};

RNCheckbox.FreeFood = ({
  value = false,
  label = "Count as free food",
  onSelect,
  isMeal,
  ...rest
}: RNCheckboxProps): React.ReactElement | null => {
  const { variant } = useTheme();
  const isSimplifiedDiet = useAppSelector(selectIsSimplifiedDiet);
  if (!isSimplifiedDiet || isMeal) {
    return null;
  }
  // Render the checkbox, checked by default when isSimplifiedDiet is true
  return (
    <RNCheckbox
      label={label}
      value={value}
      onSelect={onSelect}
      lightBorder={variant === "dark"}
      {...rest}
    />
  );
};

export default RNCheckbox;
