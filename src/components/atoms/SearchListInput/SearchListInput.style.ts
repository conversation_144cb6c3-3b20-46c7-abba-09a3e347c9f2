import { ms, ScaledSheet, vs } from "react-native-size-matters";
import { Theme } from "@/types/theme/theme";

const searchInputStyles = (theme: Theme) => ScaledSheet.create({
  searchResultsView: {
    top: ms(54),
    left: ms(0),
    right: ms(0),
    backgroundColor: theme.colors.dropDownGray,
    borderRadius: ms(8),
    zIndex: 999,
    position: 'absolute',
    borderWidth: 0.76,
    borderColor: theme.colors.mediumGray
  },
  searchResultsList: {
    flex: 1,
    maxHeight: vs(220),
    paddingHorizontal: ms(20),

  },
  relative_position: { position: "relative" },
    searchAddItemView: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    paddingLeft: ms(16),
    paddingRight: ms(24),
    paddingBottom: ms(26),
    paddingTop: ms(12)
  },
  searchItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: vs(6.5),
    borderBottomWidth: 0.75,
    borderBottomColor: theme.colors.mediumGray,
  },
  searchItemTextContainer: {
    flex: 1,
    marginRight: ms(10),
  },
  searchItemRow: {
    justifyContent: "center",
    maxWidth: '100%',
    flex:1,
    },
  itemName: {
    fontSize: ms(14),
    marginBottom: ms(3)
  },
  itemDetails: {
    fontSize: ms(12),
  },
});

export default searchInputStyles;
