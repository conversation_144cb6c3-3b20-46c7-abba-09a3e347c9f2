import { useTranslation } from "react-i18next";
import { <PERSON>ton, Typography } from "@/components/atoms";
import { ScrollView } from "react-native-gesture-handler";
import { TouchableWithoutFeedback, View } from "react-native";
import React, {
  FC,
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import Loading from "@/components/atoms/Loading/Loading";

import Step1 from "./Step1";
import Step3 from "./Step3";
import Step4 from "./Step4";
import Step6 from "./Step6";
import { useTheme } from "@/theme";
import { useAppSelector } from "@/store";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { selectTask } from "@/store/slices/taskManager/taskManager.slice";
import { getMedicationSheetStyles } from "./MedicationBottomSheet.style";
import RNBottomSheetWrapper, {
  RNBottomSheetWrapperProps,
} from "@/components/atoms/RNBottomSheetWrapper/RNBottomSheetWrapper";

export interface FormulaBottomSheetProp
  extends Partial<RNBottomSheetWrapperProps> { }

const FormulaBottomSheet: FC<FormulaBottomSheetProp> = forwardRef(
  (
    {
      formulaFormStep,
      getFrequencyName,
      populateFormulaDetails,
      handleFormulaModalBackPress,
      control,
      selectedTaskId,
      getFormulaFooterButtonText,
      getFormulaFooterButtonPress,
      handleFormulaModalClosePress,
      onDeletePress,
      isLoading,
      setFormulaValue,
      customUpdateHandler,
    },
    ref
  ) => {
    const { colors } = useTheme();
    const bottomSheetModalRef = useRef(null);
    const { t } = useTranslation(["taskManager"]);
    const [loading, setLoading] = useState(false); // Add loading state

    const styles: any = useDynamicStyles(getMedicationSheetStyles);

    const { formulaDetails, frequencies, reminderTimes, formulaUnitList } =
      useAppSelector(selectTask);

    const payloadFrequency = frequencies.find(
      (frequency) => formulaDetails?.frequencyTypeId === frequency.id
    );

    const frequencyTypeName = frequencies.find((frequency) => {
      if (formulaDetails?.customScheduleDetails) {
        return (
          formulaDetails?.customScheduleDetails?.frequencyTypeId ===
          frequency.id
        );
      }
      return formulaDetails?.frequencyTypeId === frequency.id;
    })?.text;

    const reminderTimeName = reminderTimes.find(
      (rt) => formulaDetails?.alertBefore === rt.id
    )?.text;

    const openHandler = () => {
      bottomSheetModalRef.current?.present();
    };

    const closeHandler = () => {
      bottomSheetModalRef.current?.dismiss();
      handleFormulaModalClosePress();
    };

    useImperativeHandle(
      ref,
      () => ({
        open: openHandler,
        close: closeHandler,
      }),
      []
    );

    const RenderStepComponent = () => {
      switch (formulaFormStep) {
        case 1:
          return <Step1 control={control} setFormulaValue={setFormulaValue} />; //Serving screen

        case 2:
          return (
            <Step3
              control={control}
              isFormula={true}
              getFrequencyName={getFrequencyName}
              customUpdateHandler={customUpdateHandler}
            />
          ); //schedule screen
        case 3:
          return (
            <Step6
              control={control}
              isLoading={isLoading}
              formulaDetails={formulaDetails}
              frequencyTypeName={
                formulaDetails?.customScheduleDetails
                  ? `${payloadFrequency?.text ?? ""} ( ${frequencyTypeName} )`
                  : (frequencyTypeName ?? "")
              }
            />
          ); //summary screen
        case 4:
          return <Step4 control={control} />; // custom schedule
        default:
          return <Step1 control={control} />;
      }
    };

    const hideBackButton =
      (formulaFormStep == 1 && typeof selectedTaskId !== "number") ||
      formulaFormStep == 3;


    return (
      <>
        <RNBottomSheetWrapper
          ref={bottomSheetModalRef}
          snapPoint={["92.5%"]}
          loading={loading}
          loadingComponent={<Loading />}
        >
          <View style={styles.header}>
            {hideBackButton ? (
              <View />
            ) : (
              <TouchableWithoutFeedback onPress={handleFormulaModalBackPress}>
                <Typography.B1>{t("back")}</Typography.B1>
              </TouchableWithoutFeedback>
            )}

            <View />

            <TouchableWithoutFeedback onPress={closeHandler}>
              <Typography.B1>{t("close")}</Typography.B1>
            </TouchableWithoutFeedback>
          </View>

          <ScrollView
            contentContainerStyle={styles.container}
            showsVerticalScrollIndicator={false}
            nestedScrollEnabled
          >
            <View style={{ flex: 1 }}>{RenderStepComponent()}</View>

            {formulaFormStep !== 3 ? (
              <Button.Main
                style={styles.nextBtnContainer}
                onPress={async () => {
                  const buttonText =
                    getFormulaFooterButtonText(formulaFormStep)?.toLowerCase?.() ||
                    "";
                  const isSave = buttonText.includes("save");
                  const fn = getFormulaFooterButtonPress(formulaFormStep);

                  if (isSave) {
                    setLoading(true);
                    try {
                      if (typeof fn === "function") {
                        await fn();
                      }
                      setLoading(false);
                      closeHandler();
                    } catch (e) {
                      setLoading(false);
                    }
                  } else {
                    // For non-save actions, just run the function
                    if (typeof fn === "function") {
                      await fn();
                    }
                  }
                }}
              >
                <Typography.H5 style={styles.nextBtn}>
                  {getFormulaFooterButtonText(formulaFormStep)}
                </Typography.H5>
              </Button.Main>
            ) : (
              <View>
                <Button.YellowOutline
                  style={{
                    ...styles.editBtnContainer,
                    borderColor: colors.delete,
                  }}
                  onPress={onDeletePress}
                >
                  <Typography.B1
                    style={[styles.nextBtn, { color: colors.textPrimary }]}
                  >
                    Remove
                  </Typography.B1>
                </Button.YellowOutline>
                <Button.Main
                  style={styles.nextBtnContainer}
                  onPress={async () => {
                    // No loader for Edit, just call the function
                    await populateFormulaDetails(
                      {
                        ...formulaDetails,
                        formulaUnit:
                          formulaUnitList?.find(
                            (x) => x.id === formulaDetails?.unitId
                          ) || formulaUnitList?.[0],
                        frequencyTypeId: {
                          label: payloadFrequency?.text,
                          id: formulaDetails?.frequencyTypeId,
                          value: formulaDetails?.frequencyTypeId,
                        },
                        alertBefore: {
                          label: reminderTimeName,
                          id: formulaDetails?.alertBefore,
                          value: formulaDetails?.alertBefore,
                        },
                      },
                      frequencies
                    );
                    // Do NOT call setLoading or closeHandler here
                  }}
                >
                  <Typography.B1 style={styles.nextBtn}>Edit</Typography.B1>
                </Button.Main>
              </View>
            )}
          </ScrollView>
        </RNBottomSheetWrapper>
      </>
    );
  }
);

export default FormulaBottomSheet;
