import { Fonts } from "@/constants";
import { SCREEN_HEIGHT } from "@/theme/_config";

import { Theme } from "@/types/theme/theme";
import { StyleSheet } from "react-native";
import { scale, ScaledSheet, ms } from "react-native-size-matters";

export const getMedicationSheetStyles = (theme: Theme) =>
  ScaledSheet.create({
    zIndexMax: {
      zIndex: 99999
    },
    customRow: {
      flexDirection: "row",
      alignItems: "flex-start",
      marginTop: ms(28),
    },
    marginRight30: {
      marginRight: ms(30)
    },
    frequencyLabel: {
      lineHeight: 20,
      marginBottom: 5,
      width: ms(114),
      color: theme.colors.mediumGray,
    },
    frequencyValue: {
      lineHeight: 20,
      marginBottom: 5,
    },
    flex085: {
      flex: 0.85
    },
    height28: {
      height: ms(28)
    },
    rowAlignStart: {
      flexDirection: "row",
      alignItems: "flex-start",
    },
    flex1zIndex9998: {
      flex: 1,
      zIndex: 9998
    },
    dropdownTextStyle: {
      textAlign: "center",
      maxWidth: "96%"
    },
    marginTop32ZIndexNeg1: {
      marginTop: ms(32),
      zIndex: -1
    },
    rowAlignCenterZIndexNeg1: {
      flexDirection: "row",
      alignItems: "center",
      zIndex: -1,
    },
    header: {
      flexDirection: "row",
      paddingHorizontal: scale(20),
      justifyContent: "space-between",
    },
    container: {
      flexGrow: 1,
      justifyContent: "space-between",
      paddingBottom: scale(20),
    },
    unit: {
      color: theme.colors.yellow,
      fontFamily: Fonts.RALEWAY_MEDIUM,
    },
    contentContainer: {
      padding: scale(12),
      flexGrow: 1,
      flexDirection: "column",
    },
    nextBtn: {
      fontFamily: Fonts.RALEWAY_BOLD,
      color: theme.colors.white,
    },
    nextBtnContainer: {
      marginHorizontal: scale(20),
      marginBottom: scale(20),
    },
    editBtnContainer: {
      marginHorizontal: scale(20),
      marginBottom: scale(10),
      borderRadius: 16,
    },
    inputContainer: {
      marginBottom: scale(2),
    },
    title: {
      fontSize: ms(22),
      fontFamily: Fonts.RALEWAY_BOLD,
      marginBottom: ms(26),
      textAlign: "center",
    },
    homeTimeContainer: {
      flexDirection: "row",
      alignItems: "flex-start",
    },
    homeTimeInnerContainer: {
      marginTop: ms(4),
      marginRight: ms(30),
      flexDirection: "row",
      alignItems: "flex-start",
    },
    homeTimeText: {
      lineHeight: ms(12),
      marginBottom: 5,
      marginLeft: 5,
      color: theme.colors.yellowPink,
    },
    pillIcon: {
      width: ms(58),
      height: ms(59),
    },
    iconBox: {
      width: ms(87),
      height: ms(87),
      justifyContent: "center",
      alignItems: "center",
      alignSelf: "center",
      borderColor: theme.colors.white,
      borderWidth: ms(2),
      borderRadius: ms(44),
      marginVertical: ms(16),
      padding: ms(10),
    },
    iconText: {
      fontFamily: Fonts.RALEWAY_BOLD,
      textAlign: "center",
    },
    pillItem: {
      justifyContent: "center",
      alignItems: "center",
      width: "20%",
      marginBottom: ms(20),
    },
    selectedPillItem: {
      borderColor: theme.colors.mediumGray,
      borderWidth: ms(2),
      borderRadius: ms(100),
    },
    dosage: {
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    dosageFormula: {
      color: theme.colors.mediumGray,
      marginTop: ms(4),
    },
    dosageValue: {
      color: theme.colors.yellow,
    },
    dosageContainer: {
      justifyContent: "flex-end",
      alignItems: "center",
      flex: 1,
      marginBottom: ms(56),
      marginTop: ms(30),
    },
    reminderInput: {
      paddingHorizontal: ms(12.8),
      width: "100%",
    },
    iconSection: {
      zIndex: -1,
      marginTop: ms(16),
    },
    stepperContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
    },
    stepperCircle: {
      backgroundColor: theme.colors.lighBlack,
      width: 10,
      height: 10,
      borderRadius: 5,
      marginHorizontal: 2,
    },
    errorMessage: {
      fontFamily: Fonts.RALEWAY_MEDIUM,
      color: theme.colors.delete,
      marginTop: ms(2),
    },
    addDosageBtn: {
      width: ms(106),
      height: ms(28),
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 1,
      borderColor: theme.colors.yellow,
      marginTop: ms(4),
    },
    addDosageBtnText: {
      color: theme.colors.yellow,
    },
    removeDosageBtn: {
      width: ms(106),
      height: ms(28),
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 1,
      borderColor: theme.colors.delete,
      marginTop: ms(4),
    },
    removeDosageBtnText: {
      color: theme.colors.delete,
    },
    separatorView: {
      width: "100%",
      marginVertical: ms(28),
      height: StyleSheet.hairlineWidth,
      backgroundColor: theme.colors.mediumGray,
    },
    qtyStrengthRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginTop: ms(0),
    },
    qtyStrengthBtnRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: ms(20),
    },
    buttonContainer: {
      flexDirection: "row",
      justifyContent: "flex-end",
      marginTop: ms(20),
    },

  removeButtonAlone: {
    width: ms(106),
    height: ms(28),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.delete,
    marginLeft: 'auto',
  },
  separatorIndexZero:{
    height: 0,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderColor: theme.colors.mediumGray,
    marginTop: ms(10),
    marginBottom: ms(16),
  },
  separator: {
    height: 0,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderColor: theme.colors.mediumGray,
    marginTop: ms(28),
    marginBottom: ms(16),
  },
  qtyStrengthActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%'
  },
  doseContainer: {
    flex: 1,
  },
  summaryContainer: {
    paddingHorizontal: ms(8)
  },
  loadingContainer: {
    height: SCREEN_HEIGHT * 0.45,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingIndicator: {
    color: theme.colors.primary,
    size: ms(30)
  },
  formulaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: ms(10)
  },
  formulaTextContainer: {
    flex: 1
  },
  formulaLabel: {
    marginBottom: ms(2.5),
    color: theme.colors.mediumGray
  },
  formulaName: {
    marginBottom: ms(16),
    lineHeight: ms(24),
    fontSize: ms(20)
  },
  infoFormulaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: ms(26)
  },
  infoColumn: {
    flex: 1
  },
  infoLabel: {
    marginBottom: ms(2.5),
    color: theme.colors.mediumGray
  },
  medicationNameSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: ms(10)
  },
  flex1: {
    flex: 1
  },
  labelText: {
    lineHeight: ms(20),
    marginBottom: ms(5),
    color: theme.colors.mediumGray
  },
  valueText: {
    lineHeight: ms(20),
    marginBottom: ms(5)
  },
  medicationNameText: {
    marginBottom: ms(5),
    lineHeight: ms(38)
  },
  ingredientText: {
    lineHeight: ms(20),
    marginBottom: ms(5),
    maxWidth: '86%'
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: ms(10)
  }
});
