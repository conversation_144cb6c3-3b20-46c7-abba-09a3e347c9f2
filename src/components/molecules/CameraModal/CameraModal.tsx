import CustomCamera from "@/components/atoms/CustomCamera/CustomCamera"; // Import the CustomCamera component
import { useAppDispatch, useAppSelector } from "@/store";
import {
  analyseFood,
  selectDietTracker,
  setCaptureImage,
} from "@/store/slices/dietTrackerSlice";

import { compressImage } from "@/utils/helpers";
import React, { useState } from "react";
import { Modal } from "react-native";
import { Loader } from "@/components/atoms";
import { useNavigation } from "@react-navigation/native";
import GenericModal from "../GenericModal/GenericModal";

interface CameraModalProps {
  isVisible: boolean;
  onClose: () => void;
}

const CameraModal: React.FC<CameraModalProps> = ({ isVisible, onClose }) => {
  const [showCamera, setShowCamera] = useState(false);
  const [originalCapturedPhoto, setOriginalCapturedPhoto] = useState<
    string | null
  >(null);

  const dispatch = useAppDispatch();
  const { scanStatus } = useAppSelector(selectDietTracker);

  const navigation = useNavigation();
  const [isVisibleError, setIsVisibleError] = useState(false);  

  const handlePhotoCaptured = async (uri: string) => {
    try {
      setOriginalCapturedPhoto(uri);
      const compressedUri = await compressImage(uri);
      dispatch(setCaptureImage(`file://${uri}`));

      const formData = new FormData();
      formData.append("file", {
        uri: compressedUri,
        name: "captured_image.jpg", // Replace with actual file name if available
        type: "image/jpeg",
      });

      // 3️⃣ Dispatch the `analyseFood` thunk
      const response = await dispatch(
        analyseFood({
          formData: formData, // Your FormData instance
        })
      );
      if (analyseFood.pending.match(response)) {
        setShowCamera(false);
      }
      if (analyseFood.fulfilled.match(response)) {
        setOriginalCapturedPhoto(null);
        onClose();
        navigation.navigate("AISuggestions" as never);
      }
      if (analyseFood.rejected.match(response)) {
        const errorMessage =
          typeof response.payload === "string"
            ? response.payload
            : "Failed to analyze food.";
        setIsVisibleError(true);
      }
    } catch (error) {
      console.error("❌ Failed to analyze food:", error);
      setShowCamera(false);
    }
  };

  return (
    <>    
      <Modal
        visible={isVisible}
        animationType="slide"
        transparent
        onRequestClose={onClose}
      >
        <CustomCamera
          isVisible={isVisible}
          key={`camera-${showCamera}`}
          onClose={onClose}
          onPhotoCaptured={handlePhotoCaptured}
        /> 
        <GenericModal
          isVisible={isVisibleError}
          onClose={() => {
            setIsVisibleError(false);
          }}
          headerText={"Error!"}
          bodyText={"Failed to analyze food \n Please retake photo"}
          closeText={"Cancel"}
        />
        {scanStatus === "loading" ? (
          <Loader.ScanMeal photoUri={originalCapturedPhoto} />
        ) : null}
      </Modal>
    </>
  );
};

export default CameraModal;
