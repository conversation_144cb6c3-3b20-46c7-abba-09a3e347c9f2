import React from "react";
import {
  View,
  Switch,
  Pressable,
  ActivityIndicator,
} from "react-native";
import { useTranslation } from "react-i18next";

import { useTheme } from "@/theme";
import { config } from "@/theme/_config";
import { Typography } from "@/components/atoms";
import getToggleStyles from "./SettingsToggle.styles";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";

interface SettingsToggleProps {
  title: string;
  onToggle?: (value: boolean) => void;
  isToggleOn?: boolean;
  disabled?: boolean;
  description: string;
  loading?: boolean;
}

const SettingsToggle: React.FC<SettingsToggleProps> = ({
  title = "",
  description = "",
  onToggle,
  isToggleOn,
  disabled,
  loading,
}) => {
  const { colors } = useTheme();
  const styles: any = useDynamicStyles(getToggleStyles);
  const { t } = useTranslation(["pushnotification", "common"]);

  const toggleSwitch = () => {
    onToggle?.(!isToggleOn);
  };

  return (
    <Pressable disabled={disabled}>
      <View style={styles.settingsOption}>
        <View style={styles.mainHeadingContainer}>
          <Typography.B1 style={styles.settingsOptionText}>
            {t(title as any)}
          </Typography.B1>
          <View style={styles.toggle}>
            {loading ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <Switch
                value={isToggleOn}
                disabled={disabled}
                onValueChange={toggleSwitch}
                ios_backgroundColor="#3e3e3e"
                thumbColor={isToggleOn ? "#f4f3f4" : "#f4f3f4"}
                trackColor={{ false: "#767577", true: config.colors.primary }}
              />
            )}
          </View>
        </View>

        <View style={styles.descriptionContainer}>
          <Typography.B2 style={styles.disclaimer}>
            {t(description as any)}
          </Typography.B2>
        </View>
      </View>
    </Pressable>
  );
};


export default SettingsToggle;
