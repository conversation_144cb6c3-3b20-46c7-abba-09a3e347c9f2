import React, { FC } from 'react';
import {
  FieldValues,
  UseControllerProps,
  useController
} from 'react-hook-form';
import InputControl, { InputControlProps } from '../InputControl/InputControl';
import { validateNonZeroNumericInput } from '@/utils/helpers';

interface InputFieldProps<TFieldValues extends FieldValues = FieldValues>
  extends UseControllerProps<TFieldValues> {
  component?: React.ComponentType<InputControlProps>;
  valuePropName?: string;
  trigger?: string;
  blurTrigger?: string;
  autoFocus?: boolean;
  isDecimal?: boolean;
  onFocus?: () => void; 

}

const InputField: FC<InputFieldProps> = ({
  component: Component = InputControl,
  valuePropName,
  trigger,
  blurTrigger,
  isDecimal,
  ...rest
}) => {
  const {
    field: { onBlur, onChange, value, ref },
    fieldState: { error, isTouched, invalid }
  } = useController({ ...rest });

  const decimalHandler = (text: string) => {
    if (!validateNonZeroNumericInput(text)) return;
    onChange(text);
  }

  return (
    <Component
      {...{
        [valuePropName || 'value']: value,
        [trigger || 'onChangeText']: isDecimal ? decimalHandler : onChange,
        [blurTrigger || 'onBlur']: onBlur,
        onFocus: rest.onFocus,

      }}
      inputRef={ref}
      error={(isTouched || invalid) && error?.message}
      {...rest}
    />
  );
};

export default InputField;
