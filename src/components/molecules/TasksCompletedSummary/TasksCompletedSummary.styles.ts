import { ms, ScaledSheet } from "react-native-size-matters";
import { config } from "@/theme/_config";
import { Dimensions } from "react-native";

const styles = ScaledSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    alignSelf: "center",
    height: ms(98),
    width: Dimensions.get("window").width - ms(32),
    bottom: ms(15),
    marginTop: ms(10),

  },
  profileSection: {
    flexDirection: "row",
    alignItems: "center",
    right: ms(14),
    flex:0.735
  },
  taskSection: {
    flexDirection: "row",
    flex:0.265
  },
  profileContainer: {
    position: "relative",
    justifyContent: "center",
    alignItems: "center",
  },
  svg: {
    position: "absolute",
  },
  verticalLine: {
    width: 1,
    height: ms(80),
    backgroundColor: "#E5E5E5",
    marginHorizontal: ms(10),
  },

  taskCircle: {
    width: ms(40),
    height: ms(40),
    borderRadius: 40,
    backgroundColor: config.colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: ms(8),
  },
  taskTextView: {
    maxWidth: ms(180),
    overflow: 'hidden', // Add this line
  },
  taskText: {
    paddingRight: ms(20),
  },
  greetingMessage: {
    right: ms(8),
  },
  avatarView: {
    width: ms(80),
    // height: ms(80),
    // marginLeft: ms(16),
    marginRight: ms(12),
    marginBottom: ms(24),
    flex:1,
  }
});

export default styles;
