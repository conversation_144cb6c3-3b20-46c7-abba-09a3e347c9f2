import { FC, useState } from 'react';
import { FlatList, Pressable, View } from 'react-native';
import InputField from '../Field/InputField';
import AnimatedInputDropdown from '@/components/atoms/AnimatedInputDropdown/AnimatedInputDropdown';
import { MOCK_DATA } from '@/mock-data/mockData';
import { useTranslation } from 'react-i18next';
import { useWatch } from 'react-hook-form';
import { Typography } from '@/components/atoms';
import { getCustomReminderStyle } from './CustomReminder.style';
import Icons from '@/theme/assets/images/svgs/icons';
import { config } from '@/theme/_config';
import useTaskManagerContainer from '@/containers/taskManager/useTaskManagerContainer';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';
import { useTheme } from '@/theme';

interface CustomYearlyReminderProps {
  control: any;
  inputName: string;
  options: any;
  weekIndexOptions: any;
  dayOptions: any;
}

const CustomYearlyReminder: FC<CustomYearlyReminderProps> = ({
  control,
  inputName,
  options,
  weekIndexOptions,
  dayOptions
}) => {
  const { t } = useTranslation(['taskManager']);

  const { colors } = useTheme();
  const styles: any = useDynamicStyles(getCustomReminderStyle);
  const { getFrequencyName } = useTaskManagerContainer();
  const customSchedule = useWatch({ control, name: inputName });

  function YearlyRoutineList({ onSelect, value, keyName }) {
    const [selectedMonths, setSelectedMonths] = useState<number[]>([
      ...(value?.[keyName] || {})
    ]);
    const [weekIndex, setWeekIndex] = useState(value?.['weekIndex']);
    const [weekDate, setWeekDate] = useState<number[]>(value?.['weekDays']);

    const [isEachSelected, setIsEachSelected] = useState<boolean>(
      value?.['isEachSelected']
    );

    function toggleEachSelect(val: boolean) {
      setIsEachSelected(val);
      onSelect({ ...(value || {}), ['isEachSelected']: val });
    }

    function handleMonthSelect(item: any) {
      let updatedItems: any[] = [];
      setSelectedMonths((prevState) => {
        if (prevState.includes(item.id)) {
          updatedItems = prevState.filter((id) => id !== item.id);
          return updatedItems;
        } else {
          // Add the new id (toggle on)
          updatedItems = [...prevState, item.id];
          return updatedItems;
        }
      });
      onSelect({ ...value, [keyName]: updatedItems });
    }

    function handleSelectWeekIndex(item) {
      setWeekIndex(item);
      onSelect({ ...value, ['weekIndex']: item.id });
    }

    function handleSelectWeekDays(item) {
      setWeekDate([item]);
      onSelect({ ...value, ['weekDays']: [item.id] });
    }

    const customBorderStyle = (idx: number) => {
      let styles = {};

      if (idx % 4 < 3) {
        styles = {
          borderRightColor: colors.darkGray,
          borderRightWidth: 1
        };
      }

      if (idx % 4 <= 3) {
        styles = {
          ...styles,
          borderBottomColor: colors.darkGray,
          borderBottomWidth: 1
        };
      }

      return styles;
    };

    return (
      <View>
        <Pressable
          style={[styles.inputContainer]}
          onPress={() => toggleEachSelect(true)}
        >
          <Typography.B2 style={[styles.labelStyle, {color: colors.textPrimary}]} numberOfLines={1}>
            {t('Each')}
          </Typography.B2>

          {isEachSelected && (
            <Icons.WhiteTick
              width={styles.whiteTick.width}
              height={styles.whiteTick.height}
              color={colors.textPrimary}
            />
          )}
        </Pressable>

        {isEachSelected && (
          <View style={[styles.yearMenuList]}>
            {MOCK_DATA.MEDICATION_YEARLY_ROUTINE?.map((item, idx) => (
              <Pressable
                onPress={() => handleMonthSelect(item)}
                key={item.id}
                style={[
                  styles.yearMenuItem,
                  customBorderStyle(idx),
                  selectedMonths.includes(item.id) && styles.selectedYearItem
                ]}
              >
                <Typography.B2
                  style={{ textAlign: 'center' }}
                  numberOfLines={1}
                >
                  {item.label}
                </Typography.B2>
              </Pressable>
            ))}
          </View>
        )}

        <Pressable
          style={[styles.inputContainer]}
          onPress={() => toggleEachSelect(false)}
        >
          <Typography.B2 style={[styles.labelStyle, {color: colors.textPrimary}]} numberOfLines={1}>
            {t('DaysOfWeek')}
          </Typography.B2>

          <Icons.WhiteTick
            width={styles.whiteTick.width}
            height={styles.whiteTick.height}
            color={colors.textPrimary}
          />
        </Pressable>

        <View style={{ flexDirection: 'row' }}>
          <View style={styles.menuList}>
            <FlatList
              data={weekIndexOptions} // weekoptions
              renderItem={({ item }) => (
                <Pressable
                  style={[
                    styles.menuItem,
                    item.id === weekIndex && styles.selectedMenuItem
                  ]}
                  onPress={() => {
                    handleSelectWeekIndex(item);
                  }}
                >
                  <Typography.B2 style={styles.menuItemText}>
                    {item.label}
                  </Typography.B2>
                </Pressable>
              )}
              keyExtractor={(item) => String(item.id)}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
            />
          </View>

          <View style={styles.menuList}>
            <FlatList
              data={dayOptions} //daysOptions
              renderItem={({ item }) => (
                <Pressable
                  style={[
                    styles.menuItem,
                    weekDate.includes(item.id) && styles.selectedMenuItem
                  ]}
                  onPress={() => {
                    handleSelectWeekDays(item);
                  }}
                >
                  <Typography.B2 style={styles.menuItemText}>
                    {item.label}
                  </Typography.B2>
                </Pressable>
              )}
              keyExtractor={(item) => String(item.id)}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
            />
          </View>
        </View>
      </View>
    );
  }

  return (
    <View>
      <InputField
        name={inputName}
        keyName="frequency"
        displayValue={customSchedule?.frequency?.label}
        label={t('Frequency')}
        control={control}
        visibility={true}
        trigger="onSelect"
        component={AnimatedInputDropdown}
        menu={options}
      />

      <InputField
        name={inputName}
        keyName="interval"
        displayValue={
          customSchedule?.interval?.id
            ? customSchedule?.interval?.id +
            ' ' +
            getFrequencyName(customSchedule?.frequency?.label)
            : customSchedule?.interval +
            ' ' +
            getFrequencyName(customSchedule?.frequency?.label)
        }
        label={t('Every')}
        control={control}
        trigger="onSelect"
        component={AnimatedInputDropdown}
        menu={MOCK_DATA.MEDICATION_DAILY_ROUTINE}
      />

      <Typography.B5 style={styles.smallText}>
        {t('medicationTakenEveryYear')}
      </Typography.B5>

      <InputField
        name={inputName}
        keyName="months"
        visibility={true}
        label={t('Every')}
        control={control}
        trigger="onSelect"
        component={YearlyRoutineList}
      />
    </View>
  );
};

export default CustomYearlyReminder;
