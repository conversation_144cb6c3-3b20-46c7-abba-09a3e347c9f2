import { useTheme } from "@/theme";
import { View } from "react-native";
import { ms } from "react-native-size-matters";
import React, { useState, useMemo, useEffect, useCallback } from "react";

import { truncateText } from "@/utils/helpers";
import { Typography } from "@/components/atoms";
import { useTimezone } from "@/hooks/useTimezone";
import Icons from "@/theme/assets/images/svgs/icons";
import { useAppDispatch, useAppSelector } from "@/store";
import getTimezoneOptionStyle from "./TimezoneOption.style";
import { fetchTimeZonesThunk } from "@/store/slices/settingsSlice";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import SettingsOption from "@/components/molecules/SettingsOption/SettingsOption";
import { getTimeZoneLabel, getTimeZoneOffsetDiffAdvanced } from "@/utils/timezoneUtils";
import TimeZoneBottomSheet from "@/components/molecules/TimeZoneBottomSheet/TimeZoneBottomSheet";

interface TimezoneOptionProps {
  style?: any;
  restrictFetchingUser?: boolean
}

const TimezoneOption: React.FC<TimezoneOptionProps> = ({ style, restrictFetchingUser }) => {
  const dispatch = useAppDispatch();
  const { colors } = useTheme();
  const styles: any = useDynamicStyles(getTimezoneOptionStyle);
  const { updateToNewTimeZone } = useTimezone();
  const [tzSheetVisible, setTzSheetVisible] = useState(false);
  // Use device time zone directly
  const deviceTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  // Use profile time zone from Redux
  const user = useAppSelector((state) => state.user.user);
  const [selectedTimeZone, setSelectedTimeZone] = useState(deviceTimeZone);
  const [currentTzDropdownVisible, setCurrentTzDropdownVisible] = useState(false);
  const [newTzDropdownVisible, setNewTzDropdownVisible] = useState(false);
  const { timeZones, loading } = useAppSelector((state) => state.settings);

  const handleOpenTimeZoneSheet = () => {
    setTzSheetVisible(true);
    setSelectedTimeZone(deviceTimeZone)
  };
  const handleCloseTimeZoneSheet = () => setTzSheetVisible(false);

  // Calculate difference between profileTimeZone and selectedTimeZone for the sheet
  const timeZoneDifference = useMemo(() => {
    return getTimeZoneOffsetDiffAdvanced(user?.timeZoneId, selectedTimeZone, "").formatted;
  }, [user?.timeZoneId, selectedTimeZone, timeZones?.length]);

  const timeZoneDropdownData = useMemo(() => (
    Array.isArray(timeZones)
      ? timeZones.map((tz) => ({
        id: tz?.id,
        text: tz?.displayName,
        offsetMinutes: tz?.offsetMinutes
      }))
      : []
  ), [timeZones?.length]);


  const handleUpdate = useCallback(async () => {
    try { await updateToNewTimeZone(selectedTimeZone, undefined, restrictFetchingUser); }
    catch (error) {
      console.error('Failed to update timezone:', error);
    }
  }, [selectedTimeZone, restrictFetchingUser, updateToNewTimeZone]);

  useEffect(() => {
    const fetchTimeZones = async () => {
      try {
        if (!timeZones?.length) {
          dispatch(fetchTimeZonesThunk());
        }
      } catch (error) {
        console.error('Failed to fetch time zones:', error);
      }
    };
    fetchTimeZones();
  }, []);


  useEffect(() => {
    if (!loading && tzSheetVisible) {
      handleCloseTimeZoneSheet();
    }
  }, [loading]);

  return (
    <View style={[styles.container, style]}>
      <SettingsOption
        leftIcon={
          <View style={styles.leftElements}>
            <Icons.Clock
              stroke={colors.primary}
              width={ms(16)}
              height={ms(16)}
              color={colors.textPrimary}
            />
            <Typography.B1 style={styles.leftElementsText}>
              Time zone
            </Typography.B1>
          </View>
        }
        rightIcon={
          <Icons.Pencil
            width={ms(12)}
            height={ms(12)}
            color={colors.textPrimary}
          />
        }
        tagText={<Typography.B3 style={styles.tagBold}>
          {truncateText((getTimeZoneLabel(user?.timeZoneId, timeZones)), 17)}
        </Typography.B3>
        }
        onPress={handleOpenTimeZoneSheet}
      />
      <TimeZoneBottomSheet
        isVisible={tzSheetVisible}
        onClose={handleCloseTimeZoneSheet}
        currentTimeZone={user?.timeZoneId}
        selectedTimeZone={selectedTimeZone}
        timeZoneDifference={timeZoneDifference}
        timeZoneDropdownData={timeZoneDropdownData}
        currentTzDropdownVisible={currentTzDropdownVisible}
        setCurrentTzDropdownVisible={setCurrentTzDropdownVisible}
        newTzDropdownVisible={newTzDropdownVisible}
        setNewTzDropdownVisible={setNewTzDropdownVisible}
        setSelectedTimeZone={setSelectedTimeZone}
        getTimeZoneLabel={(tz) => getTimeZoneLabel(tz, timeZones)}
        onUpdate={handleUpdate}
        loading={loading}
      />
    </View>
  );
};

export default React.memo(TimezoneOption); 