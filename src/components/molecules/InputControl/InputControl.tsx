import React, { FC, useState } from 'react';
import {
  StyleProp,
  TextInput,
  TextInputProps,
  View,
  ViewStyle
} from 'react-native';
import { useDerivedValue, useSharedValue } from 'react-native-reanimated';
import { getInputControlStyles } from './InputControl.style';
import Input, { InputProps } from '@/components/atoms/Inputs/Input';
import { Typography } from '@/components/atoms';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';

export interface InputControlProps extends TextInputProps {
  as?: React.ComponentType<InputProps>; // Adjust the type as per your needs
  label?: string;
  leftChild?: React.ComponentType;
  RightChild?: React.ReactNode;
  editable?: boolean;
  error?: string | undefined | boolean;
  containerStyle?: StyleProp<ViewStyle>;
  wrapperStyle?: StyleProp<ViewStyle>;
  value?: string;
  onBlur?: () => void;
  onFocus?: () => void;
  isPassword?: boolean;
  leftSpace?: number;
  iconSize?: number;
  animatedValue?: number;
  leftChildStyle?: StyleProp<ViewStyle>;
  rightChildStyle?: StyleProp<ViewStyle>;
  inputRef?: React.Ref<TextInput>;
}

const InputControl: FC<InputControlProps> = ({
  as: Component = Input,
  label,
  value,
  error,
  RightChild,
  editable = true,
  isPassword = false,
  containerStyle = {},
  wrapperStyle = {},
  leftChildStyle = {},
  rightChildStyle = {},
  onBlur = () => { },
  onFocus = () => { },
  leftSpace = 0,
  animatedValue = 0,
  iconSize = 18,
  ...rest
}) => {
  // initiate the theme styling

  const [showPassword, setShowPassword] = useState(false);
  const [isFocus, setIsFocus] = useState(false);
  const styles: any = useDynamicStyles(getInputControlStyles);

  // Here, we set the animation value to 1 when the user focuses on the input.
  const handleFocus = () => {
    setIsFocus(true);
    onFocus && onFocus();
  };

  // Here, we set the animation value to 0 when the user blurs the input.
  const handleBlur = () => {
    setIsFocus(false);
    onBlur && onBlur();
  };

  // This method is used to handle whether the secureTextEntry of the input text should be shown or not.
  // const handleIconPress = () => {
  //   setShowPassword(!showPassword);
  // };

  return (
    <View style={[styles.wrapper, wrapperStyle]}>
      {Boolean(label) && (
        <Typography.B2 style={[styles.labelStyle]} numberOfLines={1}>
          {label}
        </Typography.B2>
      )}
      <View style={[styles.container, containerStyle]}>
        <Component
          onFocus={handleFocus}
          onBlur={handleBlur}
          value={value}
          editable={editable}
          {...(isPassword && { secureTextEntry: !showPassword })}
          {...rest}
        />

        {/* {isPassword && (
          <View style={styles.iconWrapperRight}>
              <Pressable onPress={handleIconPress} hitSlop={defaultHitSlop}>
                  <FontIcon
                      name={showPassword ? 'eye' : 'eye-off'}
                      fontIconName={IconFont.IONICONS}
                      size={iconSize}
                      color={theme.white}
                  />
              </Pressable>
          </View>
        )} */}

        {Boolean(RightChild) && (
          <View style={styles.iconWrapperRight}>{RightChild}</View>
        )}
      </View>
      {!!error && (
        <Typography.B4 style={styles.errorMessage}>{error}</Typography.B4>
      )}
    </View>
  );
};

export default InputControl;
