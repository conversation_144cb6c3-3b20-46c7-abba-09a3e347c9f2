import React, { useCallback, useEffect, useState } from "react";
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { useSelector } from "react-redux";

import Button from "@/components/atoms/Button/Button";
import CustomTextInput from "@/components/atoms/CustomTextInput/CustomTextInput";
import Dropdown from "@/components/atoms/Dropdown/Dropdown";
import Loading from "@/components/atoms/Loading/Loading";
import Typography from "@/components/atoms/Typography/Typography";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  cleanApiState,
  createFood,
  createFoodEntry,
  fetchFoodEntries,
  fetchPortions,
  selectDietTracker,
  selectDietTrackerLoading,
  selectFoodEntrySuccess,
  selectPortions,
  setManualEntryStatus,
} from "@/store/slices/dietTrackerSlice";
import Common from "@/theme/common.style";
import { DietTrackerService } from "@/utils/dietTrackerService";
import {
  updateDateWithTimeSlot,
  validateNonZeroNumericInput,
} from "@/utils/helpers";

import { PortionConverter } from "@/utils/portions";
import { DietStatus } from "@/types/schemas/dietTracker";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getManualEntryStyles from "./ManualEntryModal.style";
import RNCheckbox from "@/components/atoms/RNCheckbox/RNCheckbox";
import BottomSheetWrapper from "@/components/atoms/BottomSheetWrapper/BottomSheetWrapper";
import { ms } from "react-native-size-matters";
import { useNavigation } from "@react-navigation/native";
import { getFoodById } from "@/services/api/dietTrackerAPI";

interface SaveMealModalProps {
  isVisible: boolean;
  onClose: () => void;
  triggeredScreen?: string;
  setPayLoadForEntry?: (payload: object) => void;
  isAlternative?: boolean;
}

const ManualEntryModal: React.FC<SaveMealModalProps> = ({
  isVisible,
  onClose,
  triggeredScreen,
  setPayLoadForEntry,
  isAlternative = false,
}) => {
  const { selectedDietDate, selectedTimeSlot } =
    useAppSelector(selectDietTracker);

  const styles: any = useDynamicStyles(getManualEntryStyles);

  const [foodName, setFoodName] = useState("");
  const [phe, setPhe] = useState("");
  const [protein, setProtein] = useState("");
  const [quantity, setQuantity] = useState("1");
  const [selectedUnit, setSelectedUnit] = useState("Servings");
  const [isDropdownVisible, setDropdownVisible] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isFreeFood, setIsFreeFood] = useState(false);
  const [errors, setErrors] = useState({
    foodName: "",
    phe: "",
    protein: "",
    quantity: "",
  });

  const portions = useSelector(selectPortions);
  const loading = useSelector(selectDietTrackerLoading);
  const success = useSelector(selectFoodEntrySuccess);
  const navigation = useNavigation();

  const dispatch = useAppDispatch();
  const [showLoader, setShowLoader] = useState(false);


  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);


  useEffect(() => {
    if (portions.length > 0) {
      setSelectedUnit(portions[0]?.text); // Set the first value as default
    }
    if (portions.length === 0) {
      dispatch(fetchPortions());
    }
  }, [dispatch, portions.length]);

  const handleTextChange = useCallback(
    (setter: (arg0: string) => void) => (text: string) => {
      if (text === "" || validateNonZeroNumericInput(text)) {
        setter?.(text);
      }
    },
    []
  );
  useEffect(() => {
    if (portions.length > 0) {
      setSelectedUnit(portions[0].text); // Set the first value as default
    }
  }, []);
  const clearStates = () => {
    setFoodName("");
    setPhe("");
    setProtein("");
    setQuantity("1");
    setSelectedUnit("Servings");
    setIsFreeFood(false);
    setErrors({
      foodName: "",
      phe: "",
      protein: "",
      quantity: "",
    });
  };

  const buildCreateFoodRequest = (
    gram_weight,
    calculated_phe,
    calculated_protein
  ) => {
    const nutrients = [
      { name: "phe", amount: calculated_phe, unit: "mg" },
      { name: "protein", amount: calculated_protein, unit: "g" },
    ];

    return {
      description: foodName?.trim(),
      category: "Custom",
      quantity: gram_weight,
      unit: "g",
      nutrients,
      portions: [{ name: selectedUnit, quantity: 1, gram_weight }],
      user_flags: { is_free: isFreeFood },
    };
  };

  const createEntryFlow = async (foodId: string) => {
    const createEntryRequest = {
      description: foodName?.trim(),
      time: updateDateWithTimeSlot(selectedDietDate, selectedTimeSlot),
      items: [
        {
          description: foodName?.trim(),
          quantity: parseFloat(quantity),
          unit: selectedUnit,
          gram_weight: PortionConverter.toGrams(selectedUnit, 1),
          phe: parseFloat(phe),
          protein: parseFloat(protein),
          food_id: foodId,
          user_flags: { is_free: isFreeFood },
          detection_id: "",
        },
      ],
    };

    const result = await dispatch(createFoodEntry(createEntryRequest));
    if (createFoodEntry.fulfilled.match(result)) {
      const formattedDate = new Date(selectedDietDate).toISOString();
      await dispatch(fetchFoodEntries({ date: formattedDate }));
      clearStates();
      setShowLoader(false)
      navigation.navigate("DietScreen");
      onClose();
      dispatch(setManualEntryStatus(DietStatus.SUCCESS));
      dispatch(cleanApiState());
    }
  };

  const fetchFoodForEntry = async (foodId: string) => {
    try {
      setShowLoader(true)
      const response = await getFoodById(
        foodId,
        parseFloat(quantity) || 1,
        selectedUnit,
        PortionConverter.toGrams(selectedUnit, 1) || 0
      );
      const updatedResponse = {
        ...response,
        isAlternative: isAlternative,
      };
      if (response) {
        setShowLoader(false)
        setPayLoadForEntry?.(updatedResponse);
        onClose();
        clearStates();
      }
    } catch (err) {
      setShowLoader(false)
      console.error("Error fetching food by ID:", err);
    }
  };

  // Handle Save & Dispatch the API call
  const handleSave = async () => {
    if (!validateFields()) return;
    let new_portions = portions.map((portion) => ({
      name: portion.text,
      quantity: 1,
      gram_weight: PortionConverter.toGrams(portion.text, 1),
    }));

    let total_gram_weight = PortionConverter.toGrams(
      selectedUnit,
      parseFloat(quantity)
    );
    let pheFactor = DietTrackerService.CalculatePheFactor(
      total_gram_weight,
      parseFloat(phe)
    );
    let proteinFactor = DietTrackerService.CalculateProteinFactor(
      total_gram_weight,
      parseFloat(protein)
    );
    let gram_weight = PortionConverter.toGrams(selectedUnit, 1);
    let calculated_phe = DietTrackerService.CalculatePhe(
      gram_weight,
      pheFactor,
      1
    );
    let calculated_protein = DietTrackerService.CalculatePhe(
      gram_weight,
      proteinFactor,
      1
    );

    const createFoodRequest = buildCreateFoodRequest(
      gram_weight,
      calculated_phe,
      calculated_protein
    );

    const createFoodResponse = await dispatch(createFood(createFoodRequest));
    if (!createFood.fulfilled.match(createFoodResponse)) return;

    const foodId = createFoodResponse.payload._id;
    if (triggeredScreen === "LogFoodMain") {
      setShowLoader(true)
      await createEntryFlow(foodId);
    } else {
      await fetchFoodForEntry(foodId);
    }
    setErrors({ foodName: "", phe: "", protein: "", quantity: "" }); // Clear errors
  };

  const validateFields = () => {
    const newErrors: { [key: string]: string } = {};
    if (!foodName.trim()) newErrors.foodName = "Food name is required.";
    if (!phe.trim()) newErrors.phe = "Phe value is required.";
    if (!protein.trim()) newErrors.protein = "Protein value is required.";
    if (!quantity.trim()) newErrors.quantity = "Quantity is required.";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  useEffect(() => {
    if (success) {
      setErrors({ foodName: "", phe: "", protein: "", quantity: "" });
    }
  }, [success]);

  const renderTitle = () => {
    return triggeredScreen === "LogFoodMain" ? "Save & Log Food" : "Continue";
  };
  const hasErrors = Object.values(errors).some((val) => val !== "");

  return (
    <BottomSheetWrapper
      isVisible={isVisible}
      onClose={() => {
        clearStates();
        onClose();
      }}
    >
      <>
        <View style={styles.header}>
          <Typography.H1 style={[Common.textBold, styles.title]}>
            Add Manually
          </Typography.H1>
        </View>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === "ios" ? "padding" : undefined}
        >
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <ScrollView
              contentContainerStyle={styles.scrollContainer}
              keyboardShouldPersistTaps="handled"
            >
              <View style={styles.container}>
                {/* Food Name Input */}
                <View>
                  <Typography.B1>Name</Typography.B1>
                  <CustomTextInput
                    placeholder="Enter your food name here"
                    value={foodName}
                    onChangeText={setFoodName}
                    errorMessage={errors.foodName}
                  />
                </View>

                {/* Phe Input */}
                <View>
                  <Typography.B1>Phe</Typography.B1>
                  <CustomTextInput
                    placeholder="Enter phe amount"
                    keyboardType="numeric"
                    value={phe}
                    onChangeText={handleTextChange(setPhe)}
                    unit="mg"
                    errorMessage={errors.phe}
                  />
                </View>

                {/* Protein Input */}
                <View>
                  <Typography.B1>Protein</Typography.B1>
                  <CustomTextInput
                    placeholder="Enter protein amount"
                    keyboardType="numeric"
                    value={protein}
                    onChangeText={handleTextChange(setProtein)}
                    unit="g"
                    errorMessage={errors.protein}
                  />
                </View>
                <View>
                  <Typography.B3 style={[Common.textBold, styles.note]}>
                    On average, 50 mg Phe = 1 g protein
                  </Typography.B3>
                </View>
                {/* Serving Input */}
                <Typography.B1>Serving</Typography.B1>
                <View
                  style={[
                    styles.servingInputContent,
                    isFocused && styles.servingTextInputFocused,
                  ]}
                >
                  <CustomTextInput
                    placeholder="Enter quantity here"
                    keyboardType="numeric"
                    value={quantity}
                    onChangeText={handleTextChange(setQuantity)}
                    style={{ flex: 1, borderWidth: 0 }}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                  />

                  <Dropdown
                    isVisible={isDropdownVisible}
                    data={portions}
                    selectedValue={selectedUnit}
                    setSelectedValue={setSelectedUnit}
                    onToggle={() => {
                      setDropdownVisible((prev) => !prev);
                      Keyboard.dismiss();
                    }}
                    screenName="ManualEntryModal"
                  />
                </View>
                {/* Error Text */}
                {errors.quantity && (
                  <Typography.B1 style={styles.errorText}>
                    {errors.quantity}
                  </Typography.B1>
                )}

                <RNCheckbox.FreeFood
                  value={isFreeFood}
                  onSelect={(val: boolean) => setIsFreeFood(val)}
                />

                {/* Save Button */}
                <View
                  style={[
                    styles.saveButtonContainer,
                    { marginTop: hasErrors ? ms(40) : ms(90) },
                  ]}
                />

                <Button.Main onPress={handleSave} disabled={loading}>
                  <Typography.B1 style={[Common.textBold, { color: "white" }]}>
                    {renderTitle()}
                  </Typography.B1>
                </Button.Main>
              </View>
            </ScrollView>
          </TouchableWithoutFeedback>
        </KeyboardAvoidingView>

        {showLoader && <Loading />}
      </>
    </BottomSheetWrapper>
  );
};

export default ManualEntryModal;
