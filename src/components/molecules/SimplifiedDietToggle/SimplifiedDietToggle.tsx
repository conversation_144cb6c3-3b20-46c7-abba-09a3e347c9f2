import React, { useState } from "react";
import {
  View,
  Switch,
  Pressable,
  ActivityIndicator,
  TouchableOpacity
} from "react-native";
import { ms } from "react-native-size-matters";
import { useTranslation } from "react-i18next";

import { useTheme } from "@/theme";
import { config } from "@/theme/_config";
import { Typography } from "@/components/atoms";
import Icons from "@/theme/assets/images/svgs/icons";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getSimplifiedDietToggleStyles from "./SimplifiedDietToggle.styles";
import SimplifiedDietInfoModal from "./SimplifiedDietInfoModal";

interface SimplifiedDietToggleProps {
  isToggleOn?: boolean;
  onToggle?: (value: boolean) => void;
  disabled?: boolean;
  loading?: boolean;
}

const SimplifiedDietToggle: React.FC<SimplifiedDietToggleProps> = ({
  isToggleOn,
  onToggle,
  disabled,
  loading
}) => {
  const styles: any = useDynamicStyles(getSimplifiedDietToggleStyles);
  const { colors } = useTheme();
  const { t } = useTranslation(['diet']);
  const [isInfoModalVisible, setIsInfoModalVisible] = useState(false);

  const toggleSwitch = () => {
    onToggle?.(!isToggleOn);
  };

  const toggleInfoModal = () => {
    setIsInfoModalVisible(!isInfoModalVisible);
  };

  return (
    <>
      <Pressable disabled={disabled}>
        <View style={styles.container}>
          <View style={styles.headerContainer}>
            <View style={styles.titleContainer}>
              <Typography.B1 style={styles.title}>{t('simplifiedDiet.title')}</Typography.B1>
              <TouchableOpacity style={{ marginBottom: 2 }} onPress={toggleInfoModal}>
                <Icons.Info
                  width={ms(16)}
                  height={ms(16)}
                  color={colors.textPrimary}
                  hasNoCircle={false}
                />
              </TouchableOpacity>
            </View>
            <View style={styles.toggle}>
              {loading ? (
                <ActivityIndicator size="small" color={colors.primary} />
              ) : (
                <Switch
                  trackColor={{ false: "#767577", true: config.colors.primary }}
                  thumbColor={isToggleOn ? "#f4f3f4" : "#f4f3f4"}
                  ios_backgroundColor="#3e3e3e"
                  onValueChange={toggleSwitch}
                  value={isToggleOn}
                  disabled={disabled}
                />
              )}
            </View>
          </View>
          <Typography.B2 style={styles.disclaimer}>
            {t('simplifiedDiet.disclaimer')}
          </Typography.B2>
        </View>
      </Pressable>

      {/* Info Modal */}
      <SimplifiedDietInfoModal
        isVisible={isInfoModalVisible}
        onClose={toggleInfoModal}
      />
    </>
  );
};

export default SimplifiedDietToggle;
