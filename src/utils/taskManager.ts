// Helper method for handling timestamps with Day.js
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

type TimestampHelper = {
  getIntakeTime: (timestamp: number) => string;
  convertUtcLocal: (timestamp: number, toUTC?: boolean) => number;
  appendIntakeTime: (date: string, intakeTime: string, toUTC?: boolean) => string;
  getTimestampFromDate: (date: string) => number;
  combineDateAndTimeToUTC: (fromDate: string, intakeTime: string) => { fromDate: string; intakeTime: string };
};

export const handleTimestamp: TimestampHelper = {
  /**
   * Converts a timestamp to a time string in 24-hour format (e.g., "17:47:00").
   * @param {number} timestamp - The timestamp to convert (e.g., 1737312420000).
   * @returns {string} - The time in 24-hour format.
   */
  getIntakeTime: (timestamp: number): string => {
    return dayjs(timestamp).format("HH:mm:ss");
  },

  /**
   * Converts a UTC timestamp to local time or vice versa.
   * @param {number} timestamp - The UTC timestamp (e.g., 1737312420000).
   * @param {boolean} [toUTC=false] - If true, converts local time to UTC. If false, converts UTC to local time.
   * @returns {number} - The converted timestamp.
   */
  convertUtcLocal: (timestamp: number, toUTC = false): number => {
    if (toUTC) {
      return dayjs(timestamp).utc().valueOf();
    } else {
      return dayjs(timestamp).local().valueOf();
    }
  },

  /**
   * Appends intakeTime to the given date, replacing the original time.
   * Returns both local and UTC versions.
   * @param {string} date - The ISO string of the date to modify (e.g., "2025-01-19T12:47:53.999Z").
   * @param {string} intakeTime - The intake time in 24-hour format (e.g., "17:47:00").
   * @param {boolean} [toUTC=false] - If true, returns the UTC version; otherwise, returns the local version.
   * @returns {string} - The modified ISO date string with the appended intakeTime.
   */
  appendIntakeTime: (date: string, intakeTime: string, toUTC = false): string => {
    const baseDate = dayjs(date).format("YYYY-MM-DD");
    const combinedDateTime = `${baseDate}T${intakeTime}`;

    if (toUTC) {
      return dayjs(combinedDateTime).utc().toISOString();
    } else {
      return dayjs(combinedDateTime).local().toISOString();
    }
  },
  /**
 * Converts a date string to a timestamp.
 * @param {string} date - The date string (e.g., "2025-01-19T12:47:53.999Z").
 * @returns {number} - The timestamp in milliseconds.
 */
  getTimestampFromDate: (date: string): number => {
    return dayjs(date).valueOf();
  },
  /**
   * Combines a date and a time into a local datetime string and time string.
   * @param {string} fromDate - The date string (e.g., "2025-01-23T00:00:00.000Z" or local ISO string)
   * @param {string} intakeTime - The time string or timestamp (e.g., "14:24:01.578Z" or local time)
   * @returns {{ fromDate: string; intakeTime: string }} - Local datetime ISO string and time string
   */
  combineDateAndTimeToUTC: (fromDate: string, intakeTime: string): { fromDate: string; intakeTime: string } => {
    // Parse both dates into Date objects (local time)
    const fromDateObj = new Date(fromDate); // Local date
    const intakeTimeObj = new Date(intakeTime); // Local time

    // Extract the time portion from intakeTime (local)
    const hours = intakeTimeObj.getHours();
    const minutes = intakeTimeObj.getMinutes();
    const seconds = intakeTimeObj.getSeconds();
    const milliseconds = intakeTimeObj.getMilliseconds();

    // Update the fromDateObj with the extracted time (local)
    fromDateObj.setHours(hours, minutes, seconds, milliseconds);

    // Format the extracted time as HH:mm:ss
    const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    // Format fromDateObj as local ISO string (yyyy-MM-ddTHH:mm:ss.sss)
    const pad = (n: number) => n.toString().padStart(2, '0');
    const padMs = (n: number) => n.toString().padStart(3, '0');

    const localISOString = `${fromDateObj.getFullYear()}-${pad(fromDateObj.getMonth() + 1)}-${pad(fromDateObj.getDate())}T${pad(hours)}:${pad(minutes)}:${pad(seconds)}.${padMs(milliseconds)}`;


    return ({
      fromDate: localISOString,
      intakeTime: formattedTime, // Only the time part
    });
  }
};



/**
 * Example Usage:
 *
 * const timestamp = 1737312420000; // Example timestamp
 * const intakeTime = handleTimestamp.getIntakeTime(timestamp); // "17:47:00"
 *
 * const localTimestamp = handleTimestamp.convertUtcLocal(timestamp, false); // UTC to local
 * const utcTimestamp = handleTimestamp.convertUtcLocal(timestamp, true);  // Local to UTC
 *
 * const modifiedLocalDate = handleTimestamp.appendIntakeTime("2025-01-19T12:47:53.999Z", intakeTime, false);
 * const modifiedUtcDate = handleTimestamp.appendIntakeTime("2025-01-19T12:47:53.999Z", intakeTime, true);
 */


type TimeOfDay = "Morning" | "Afternoon" | "Evening";

export function getPeriodOfDay(intakeTime: string | null): TimeOfDay | null {
  /**
   * Determines the time of day (Morning, Afternoon, Evening) based on the provided time.
   * - Morning (12:00 AM - 11:59 AM)
   * - Afternoon (12:00 PM - 5:59 PM)
   * - Evening (6:00 PM - 11:59 PM)
   *
   * @param intakeTime - A string in the format "HH:mm" or null
   * @returns The corresponding period of the day as "Morning", "Afternoon", or "Evening", or null if invalid.
   */

  if (!intakeTime) {
    return null; // Handle null intakeTime gracefully
  }

  // Parse the input time
  const [hours, minutes] = intakeTime.split(":").map(Number);

  if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours >= 24 || minutes < 0 || minutes >= 60) {
    return null; // Invalid time format
  }

  const totalMinutes = hours * 60 + minutes;

  if (totalMinutes >= 0 && totalMinutes < 12 * 60) {
    return "Morning";
  } else if (totalMinutes >= 12 * 60 && totalMinutes < 18 * 60) {
    return "Afternoon";
  } else {
    return "Evening";
  }
}

