import { SearchBy } from '@/constants/searchFood';
import { selectConsumptionUnit } from '@/store/slices/settingsSlice';
import { generateUniqueId, removeTrailingZeros, roundNumber } from '@/utils/helpers';
import { useSelector } from 'react-redux';

export enum weight_unit {
	g = 1,
	mg = 2,
}

export class DietTrackerService {
	static ConvertToGram(unit: string, weight: number): number {
		if (unit == weight_unit[weight_unit.g]) return weight;
		return weight * 1000;
	}

	static ConvertToMiliGram(unit: string, weight: number): number {
		if (unit == weight_unit[weight_unit.g]) return weight / 1000;
		return weight;
	}

	static CalculatePheFactor(gram_weight: number, phe: number): number {
		return phe / gram_weight;
	}

	static CalculateProteinFactor(gram_weight: number, protein: number): number {
		return protein / gram_weight;
	}

	static CalculateProtein(gram_weight: number, proteinFactor: number, quantity: number): number {
		return gram_weight * proteinFactor * quantity;
	}

	static CalculatePhe(gram_weight: number, pheFactor: number, quantity: number): number {
		return gram_weight * pheFactor * quantity;
	}

	static CalculatePheFromProtein(protein: number) {
		return protein * 50;
	}

	static ConvertToSearchFoodResponse(response: any, consumptionType: string): FoodSearchResponse[] {
		if (!response) return [];

		let foodResponse = response.map((food: any) => {
			let foodEntry = food.item;
			let phe_text = `${roundNumber(parseInt(removeTrailingZeros(foodEntry.phe ?? 0)))} mg ` + (consumptionType === 'Protein' ? 'PRO' : 'Phe') + ` per ${foodEntry.unit}`;

			let fe_response = {
				id: foodEntry.food_id,
				description: foodEntry.description,
				pheText: phe_text,
				phe: removeTrailingZeros((foodEntry.phe ?? 0).toFixed(2)),
				protein: removeTrailingZeros((foodEntry.protein ?? 0).toFixed(2)),
				unit: foodEntry.unit,
				quantity: foodEntry.quantity,
				editQuantity: foodEntry.quantity,
				editUnit: foodEntry.unit,
				editGramWeight: foodEntry.gram_weight,
				user_flags: foodEntry?.user_flags,
			} as FoodSearchResponse;

			return fe_response;
		});
		return foodResponse;
	}

	static ConvertToSearchFreqFoodResponse(response: any, consumptionType: 'Phe' | 'Protein', isSimplifiedDiet: boolean = false): FoodSearchResponse[] {
		if (!response) return [];

		let foodResponse = response.map((food: any) => {
			let foodEntry = food.item;
			const nutrients = this.calculateMealNutrients(foodEntry, isSimplifiedDiet);

			const PheOrProteinHash = {
				Protein: nutrients.protein,
				Phe: nutrients.phe,
			};

			let phe_text = `${roundNumber(parseInt(removeTrailingZeros(PheOrProteinHash[consumptionType] ?? 0)))} mg ` + (consumptionType === 'Protein' ? 'PRO' : 'Phe') + ` per ${foodEntry.unit}`;

			let fe_response = {
				id: foodEntry.food_id,
				description: foodEntry.description,
				pheText: phe_text,
				phe: removeTrailingZeros(nutrients.phe.toFixed(2)),
				protein: removeTrailingZeros(nutrients.protein.toFixed(2)),
				unit: foodEntry.unit,
				quantity: foodEntry.quantity,
				editQuantity: foodEntry.quantity,
				editUnit: foodEntry.unit,
				editGramWeight: foodEntry.gram_weight,
				user_flags: foodEntry?.user_flags,
			} as FoodSearchResponse;

			return fe_response;
		});
		return foodResponse;
	}

	static CalculatePheProteinFromSearchFoodResponse(response: any, consumptionType: 'Protein' | 'Phe', isSimplifiedDiet: boolean) {
		// console.log(consumptionType);
		const filteredData = response;
		if (!filteredData) return [];
		return filteredData.map((item: any) => {
			const portion = item.food?.portions[0] || null;
			const { protein_amount, phe_amount } = this.calculatePheProteinAmount(
				item.food.nutrients,
				item.food?.portions[0].gram_weight || null,
				item.food.quantity,
				item.food.category == 'Meal' || (item.food.ingredients && item.food.ingredients.length > 0)
			);

			let foodEntry = {
				food_details: item.food,
			};
			const nutrients = this.calculateMealNutrients(foodEntry, isSimplifiedDiet);

			const PheOrProteinHash = {
				Protein: nutrients.protein,
				Phe: nutrients.phe,
			};

			const valueToUse = isSimplifiedDiet && item.food.category == 'Meal' ? PheOrProteinHash[consumptionType] : consumptionType === 'Protein' ? protein_amount : (phe_amount ?? 0);

			const phe_text =
				`${roundNumber(removeTrailingZeros(valueToUse.toFixed(2)))} ${consumptionType === 'Protein' ? 'g' : 'mg'} ` + (consumptionType === 'Protein' ? 'PRO' : 'Phe') + ` per ${portion?.name}`;

			return {
				id: item.food?._id,
				description: item.food?.description,
				pheText: phe_text,
				phe: removeTrailingZeros((phe_amount ?? 0).toFixed(2)),
				protein: removeTrailingZeros((protein_amount ?? 0).toFixed(2)),
				unit: item?.food?.unit,
				quantity: item.food?.quantity || 0,
				isFreeFood: item?.food?.user_flags?.is_free || false,
			};
		});
	}

	static onQtyChange(proteinFactor: number, pheFactor: number, quantity: number, gram_weight: number): onFoodFormulaChangeResponse {
		if (gram_weight == 0) return { quantity: 0, protein: 0, phe: 0 } as onFoodFormulaChangeResponse;
		let protein = this.CalculateProtein(gram_weight, proteinFactor, quantity);
		let phe = this.CalculatePhe(gram_weight, pheFactor, quantity);
		return { quantity, protein, phe } as onFoodFormulaChangeResponse;
	}

	static onProteinChange(proteinFactor: number, pheFactor: number, protein: number, gram_weight: number): onFoodFormulaChangeResponse {
		if (proteinFactor == 0 || gram_weight == 0) return { quantity: 0, protein: 0, phe: 0 } as onFoodFormulaChangeResponse;
		let quantity = protein / (proteinFactor * gram_weight);
		let phe = this.CalculatePhe(gram_weight, pheFactor, quantity);
		return { quantity, protein, phe } as onFoodFormulaChangeResponse;
	}

	static onPheChange(proteinFactor: number, pheFactor: number, phe: number, gram_weight: number): onFoodFormulaChangeResponse {
		if (pheFactor == 0 || gram_weight == 0) return { quantity: 0, protein: 0, phe: 0 } as onFoodFormulaChangeResponse;
		let quantity = phe / (pheFactor * gram_weight);
		let protein = this.CalculateProtein(gram_weight, proteinFactor, quantity);
		return { quantity, protein, phe } as onFoodFormulaChangeResponse;
	}

	static ConvertToFoodByIdResponse(response: any, Editquantity: number, selectedUnit: string, isSimplifiedDiet: boolean, gram_weight?: number, analysis_id?: string): FoodByIdResponse {
		let quantity = 1;
		let portion = response.portions[0];

		let { protein_amount, phe_amount, phe_Factor, protein_Factor } = this.calculatePheProteinAmount(
			response.nutrients,
			response?.portions[0].gram_weight || null,
			response.quantity,
			response.category == 'Meal' || (response.ingredients && response.ingredients.length > 0)
		);
		if (selectedUnit != undefined && selectedUnit != '') {
			portion = response.portions.find((x: Portion) => x.name.toLowerCase() == selectedUnit.toLowerCase());
			if (portion == undefined) {
				portion = {
					name: selectedUnit,
					quantity: 1,
					gram_weight: gram_weight || 1 / Editquantity,
				};
				response.portions.push(portion);
			}
		} else {
			portion = response.portions[0];
		}

		if (response.category.toLowerCase() == 'meal' && response.ingredients && response.ingredients.length > 0 && isSimplifiedDiet) {
			protein_Factor = 0;
			phe_Factor = 0;

			const getTotals = response.ingredients
				.filter((ing: any) => !ing.user_flags?.is_free) // keep only non-free ones
				.reduce(
					(totals, ing) => {
						const amount = (name: string) => ing.nutrients.find((n: any) => n.name === name)?.amount ?? 0;

						totals.phe += amount('phe');
						totals.protein += amount('protein');
						return totals;
					},
					{ phe: 0, protein: 0 }
				);
			protein_Factor = getTotals.protein / (Editquantity * portion.gram_weight);
			phe_Factor = getTotals.phe / (Editquantity * portion.gram_weight);
		}

		quantity = Editquantity;
		phe_amount = phe_Factor * portion.gram_weight * Editquantity;
		protein_amount = protein_Factor * portion.gram_weight * Editquantity;
		let portions = response.portions.map((portion: any) => ({
			name: portion.name,
			quantity: portion.quantity,
			gram_weight: portion.gram_weight,
			sequence_number: portion.sequence_number,
		}));

		if (response.category.toLowerCase() != 'custom' && response.category.toLowerCase() != 'meal') {
			if (!response.portions.some((portion: any) => portion.name.toLowerCase() === 'g')) {
				portions.push({
					name: 'g',
					quantity: 1,
					gram_weight: 1,
					sequence_number: 98,
				});
			}
			if (!response.portions.some((portion: any) => portion.name.toLowerCase() === 'oz')) {
				portions.push({
					name: 'oz',
					quantity: 1,
					gram_weight: 28.3495,
					sequence_number: 99,
				});
			}
		}
		return {
			analysis_id: analysis_id,
			_id: response._id,
			client_id: response.client_id,
			description: response.description,
			original_description: response.original_description,
			category: response.category,
			provider: response.provider,
			quantity: quantity,
			unit: portion.name,
			gram_weight: portion.gram_weight,
			protein: protein_amount,
			phe: phe_amount,
			nutrients: response.nutrients.map((nutrient: any) => ({
				name: nutrient.name,
				amount: nutrient.amount,
				unit: nutrient.unit,
			})),
			portions: portions,
			factors: {
				proteinFactor: protein_Factor,
				pheFactor: phe_Factor,
			},
			isFreeFood: !!response?.user_flags?.is_free,
			ingredients: response?.ingredients || [],
		} as FoodByIdResponse;
	}

	// static ConvertMatchFoodToFoodByIdResponse(response: any): FoodByIdResponse[] {
	// 	console.log(response,"response of our scanned food");
	// 	let result = response.results.map((result: any) => {
	// 		let food = result.matches[0].food;
	// 				// console.log({ response, selectedUnit, gram_weight, Editquantity });
	// 		return DietTrackerService.ConvertToFoodByIdResponse(food.food_details, food.quantity, food.unit, food.food_details.quantity, response.analysis_id);
	// 	});
	// 	console.log(result,"result of the matched food");
	// 	return result;
	// }

	static ConvertMatchFoodToFoodByIdResponse(response: any): {
  result: (FoodByIdResponse & { alternatives?: FoodByIdResponse[] })[];
  analysis_id: string;
}  {
	const result = response?.results?.map((result: any) => {
		const matches = result?.matches;
		const detectionId = result?.detection_id;
		const mainMatch = matches?.filter((m: any) => m?.is_best_match)[0];
		
		const mainItem = {
			...DietTrackerService.ConvertToFoodByIdResponse(
				mainMatch?.food?.food_details,
				mainMatch?.food?.quantity,
				mainMatch?.food?.unit,
				mainMatch?.food?.food_details?.quantity,
				mainMatch?.food?.gram_weight,
				response?.analysis_id
			),
			detection_id: detectionId
		};

		const alternatives = matches
			.filter((m: any) => m !== mainMatch)
			.map((alt: any) => ({
				...DietTrackerService.ConvertToFoodByIdResponse(
					alt?.food?.food_details,
					alt?.food?.quantity,
					alt?.food?.unit,
					alt?.food?.food_details?.quantity,
					alt?.food?.gram_weight,
					response?.analysis_id
				),
				detection_id: detectionId
			}));

		// Attach alternatives
		return {
			...mainItem,
			alternatives,
			_localId: generateUniqueId(mainItem),
		};
	});
	const finalResult = {
		result,
		analysis_id:response?.analysis_id

	};
	console.log(result, "result of the matched food");
	return finalResult;
}


	static ConvertToListEntriesResponse(response: ListEntriesResponse[]) {
		return response.map((ListEntriesResponse: ListEntriesResponse) => {
			let food = ListEntriesResponse.items[0] as EntryFoodDetail;
			let items = [] as EntryFoodDetail[] | undefined;

			if (food.food_details?.category.toLowerCase() == 'meal') {
				items = ListEntriesResponse.items[0].food_details?.ingredients.map((ingredient: Ingredient) => {
					return {
						description: ingredient.description,
						quantity: ingredient.quantity * food.quantity,
						unit: ingredient.unit,
						time: ListEntriesResponse.time,
						isFreeFood: !!ingredient?.user_flags?.is_free,
						protein: removeTrailingZeros((ingredient.nutrients.find((x: Nutrient) => x?.name?.toLowerCase() === 'protein')?.amount || 0) * food.quantity),
						phe: removeTrailingZeros((ingredient.nutrients.find((x: Nutrient) => x?.name?.toLowerCase() === 'phe')?.amount || 0) * food.quantity),
					} as EntryFoodDetail;
				});
			} else {
				items = ListEntriesResponse.items.map((item: EntryFoodDetail) => {
					return {
						description: item.description,
						quantity: item.quantity,
						unit: item.unit,
						time: ListEntriesResponse.time,
						protein: removeTrailingZeros(item.protein),
						phe: removeTrailingZeros(item.phe),
						food_id: item.food_id,
						gram_weight: item.gram_weight,
						isFreeFood: !!item?.user_flags?.is_free,
					} as EntryFoodDetail;
				});
			}

			return {
				_id: ListEntriesResponse._id,
				client_id: ListEntriesResponse.client_id,
				description: ListEntriesResponse.description,
				user_id: ListEntriesResponse.user_id,
				time: ListEntriesResponse.time,
				food_id: food.food_id,
				category: food.food_details?.category,
				quantity: food.quantity,
				unit: food.unit,
				gram_weight: food.gram_weight,
				phe: removeTrailingZeros(parseFloat(food.phe).toFixed(2)),
				protein: removeTrailingZeros(parseFloat(food.protein).toFixed(2)),
				items: items,
			};
		});
	}

	static calculatePheProteinAmount(nutrients: Nutrient[], gram_weight: number, edit_quantity: number, isMeal: boolean) {
		let protein_amount = 0;
		let protein_Factor = 0;
		let phe_amount = 0;
		let phe_Factor = 0;
		let protein_object = nutrients.find((x: Nutrient) => x?.name?.toLowerCase() === 'protein') ?? null;
		protein_amount = protein_object ? this.ConvertToGram(protein_object.unit, protein_object.amount) : 0;
		protein_Factor = this.CalculateProteinFactor(gram_weight, protein_amount);
		let phe = nutrients.find((x: Nutrient) => x?.name?.toLowerCase() === 'phe') ?? null;
		phe_amount = phe?.amount ?? this.CalculatePheFromProtein(protein_amount);
		phe_Factor = this.CalculatePheFactor(gram_weight, phe_amount);
		if (!isMeal) {
			phe_Factor = this.CalculatePheFactor(edit_quantity, phe_amount);
			protein_Factor = this.CalculateProteinFactor(edit_quantity, protein_amount);
			phe_amount = phe_Factor * gram_weight;
			protein_amount = protein_Factor * gram_weight;
		}
		return { protein_amount, phe_amount, phe_Factor, protein_Factor };
	}

	static calculatePheOrProtein(pheOrProtein_factor: number, gramWeight: number, quantity: number) {
		return pheOrProtein_factor * gramWeight * quantity;
	}

	static calculateMealNutrients(foodEntry: any, simplifiedMode: boolean): { phe: number; protein: number } {
		if (!foodEntry?.food_details) return { phe: 0, protein: 0 };

		const foodDetails = foodEntry.food_details;
		if (foodDetails.category !== 'Meal' || !foodDetails.ingredients || foodDetails.ingredients.length <= 1) {
			return {
				phe: parseFloat(removeTrailingZeros((foodEntry.phe ?? 0).toFixed(2))),
				protein: parseFloat(removeTrailingZeros((foodEntry.protein ?? 0).toFixed(2))),
			};
		}

		let totalPhe = 0;
		let totalProtein = 0;

		foodDetails.ingredients.forEach((ingredient: any) => {
			// Skip free foods in simplified mode
			if (simplifiedMode && ingredient?.user_flags?.is_free) {
				return;
			}

			const pheNutrient = ingredient.nutrients.find((n: any) => n?.name?.toLowerCase() === 'phe');
			const proteinNutrient = ingredient.nutrients.find((n: any) => n?.name?.toLowerCase() === 'protein');

			// Multiply by entry quantity to account for serving size
			const quantity = foodEntry.quantity || 1;
			if (pheNutrient) {
				totalPhe += pheNutrient.amount * quantity;
			}
			if (proteinNutrient) {
				totalProtein += proteinNutrient.amount * quantity;
			}
		});

		return {
			phe: parseFloat(removeTrailingZeros(totalPhe.toFixed(2))),
			protein: parseFloat(removeTrailingZeros(totalProtein.toFixed(2))),
		};
	}
}
