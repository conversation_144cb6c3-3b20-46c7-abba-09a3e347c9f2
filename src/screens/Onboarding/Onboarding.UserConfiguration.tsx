import React, { useEffect, useState } from "react";
import { Linking, ScrollView, View, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import RNFS from "react-native-fs";

import { useNavigation } from "@react-navigation/native";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  updateSimplifiedDietThunk,
  updateUser,
  updateUserProfileByPatchThunk,
  userOnboardingThunk,
} from "@/store/slices/userSlice";

import * as ImagePicker from "react-native-image-picker";

import Common from "@/theme/common.style";
import {
  getAllowanceLabel,
  getAllowancePlaceholder,
  getConvertedAllowance,
  getPheAllowanceUnit,
  setPheAllowanceValue,
  uploadFileToAzure,
} from "@/utils/helpers";
import Loading from "@/components/atoms/Loading/Loading";
import { useRemoteConfig } from "@/context/RemoteConfigContext";
import {
  createPheAllowance,
  fetchDailyConsumedPheAllowance,
  fetchPheAllowances,
  setDailyPhe,
} from "@/store/slices/pheAllowanceSlice";
import { PheAllowance } from "@/types/schemas/pheAllowance";
import { selectDietTracker } from "@/store/slices/dietTrackerSlice";
import { UserOnboarding } from "@/types/schemas/user";
import { Button, Loader, Typography } from "@/components/atoms";
import { useAppDispatch, useAppSelector } from "@/store";
import { getOnboardingStyle } from "./Onboarding.style";
import { getPreSignedUrl } from "@/services/api/userAPI";
import InputField from "@/components/molecules/Field/InputField";
import RNCheckbox from "@/components/atoms/RNCheckbox/RNCheckbox";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import TabSelector from "@/components/atoms/TabSelector/TabSelector";
import ProfileHeader from "@/components/molecules/ProfileHeader/ProfileHeader";
import { ONBOARDING_USER_VALIDATION_SCHEMA } from "@/shared/input-validations/onboarding-user-validation";
import { useAuth0 } from "react-native-auth0";
import SimplifiedDietToggle from "@/components/molecules/SimplifiedDietToggle";
import {
  selectConsumptionUnit,
  setConsumptionUnit,
  setIsSimplifiedDiet as setSimplifiedDiet,
} from "@/store/slices/settingsSlice";
import { addVersionManually } from "@/utils/versionStorage";
import DeviceInfo from "react-native-device-info";
import { TimezoneService } from "@/utils/timezoneService";

const UserConfiguration = () => {
  const navigation = useNavigation();
  const { t } = useTranslation(["onBoardingUser"]);
  const { user } = useAppSelector((state) => state.user);
  const { user: authUser } = useAuth0();
  const dispatch = useAppDispatch();
  const [selectedTab, setSelectedTab] = useState("Patient");
  const consumptionType = useAppSelector(selectConsumptionUnit);
  const [isSimplifiedDiet, setIsSimplifiedDiet] = useState(false);
  const [avatar, setAvatar] = useState({});
  const [preSignedUrl, setPreSignUrl] = useState("");
  const [profileImage, setProfileImage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [profileName, setProfileName] = useState("");
  const { remoteConfig } = useRemoteConfig();
  const { selectedDietDate } = useAppSelector(selectDietTracker);
  const [selectedConsumption, setSelectedConsumption] = useState<string>("Phe");

  const styles: any = useDynamicStyles(getOnboardingStyle);

  const { control, handleSubmit, setValue, trigger } = useForm<UserOnboarding>({
    mode: "all",
    resolver: yupResolver(ONBOARDING_USER_VALIDATION_SCHEMA),
    context: { consumptionType },
    defaultValues: {
      name: user?.name || "",
      email: user?.email || "",
      phoneNumber: user?.phoneNumber || "",
      isCareTaker: false,
      agreedToTerms: false,
      dataUsageConsent: false,
      subscribedToUpdates: false,
    },
  });

  React.useEffect(() => {
    dispatch(setConsumptionUnit("Phe"));
    let mounted = true;
    if (mounted) {
      addVersionManually(DeviceInfo.getVersion());
    }
    return () => {
      mounted = false;
    };
  }, []);


  async function handleContinue(data: UserOnboarding) {
    const { name, ...onboardingData } = data as UserOnboarding;

    const onboardingRequest = {
      ...onboardingData,
      isCareTaker: selectedTab === "Carer",
      name: profileName || name,
    };

    // return
    try {
      setIsLoading(true);
      const result = await dispatch(userOnboardingThunk(onboardingRequest));

      if (userOnboardingThunk.fulfilled.match(result)) {
        try {
          const typedAvatar = avatar as { uri?: string; fileName?: string };
          const userObject = {
            ...user,
            ...onboardingRequest,
            profilePictureUrl: typedAvatar.fileName,
            timeZoneId: TimezoneService.getDeviceTimeZone() // since user is signing up so only in this case the device timezone will be the user default selected timezone
          };

          await uploadProfileImage();
          dispatch(updateUser(userObject));
          dispatch(setSimplifiedDiet(isSimplifiedDiet));
          dispatch(
            updateSimplifiedDietThunk({ isSimplifiedDiet: isSimplifiedDiet })
          );
          await updatePheAllowance(
            onboardingRequest?.pheAllowance?.toString()
          );

          (navigation.navigate as any)("UserPreferences");
        } catch (error) {
          console.error("Error during post-onboarding steps:", error);
        }
      } else if (userOnboardingThunk.rejected.match(result)) {
        console.error("Unable to onboard user", result);
      }
    } catch (error) {
      console.error("Error during onboarding:", error);
    } finally {
      setIsLoading(false);
    }
  }

  const updatePheAllowance = async (dailyPheAllowance: string) => {
    const body: PheAllowance = {
      pheAllowanceAmount: setPheAllowanceValue(
        dailyPheAllowance,
        consumptionType
      ),
      pheAllowanceUnit: getPheAllowanceUnit(consumptionType),
    };

    dispatch(createPheAllowance(body))
      .unwrap()
      .then(() => {
        const formattedDate = new Date(selectedDietDate).toISOString(); // Extract the date part in YYYY-MM-DD format

        dispatch(fetchDailyConsumedPheAllowance(formattedDate));
        dispatch(fetchPheAllowances());
      })
      .catch((error) => {
        // console.error("createPheAllowance error: ", error);
      });
  };

  function handleTermsAndConditions() {
    Linking.openURL(remoteConfig.linkTermsAndConditions);
  }

  async function uploadProfileImage() {
    try {
      const typedAvatar = avatar as { uri?: string; fileName?: string };
      if (typedAvatar?.uri && preSignedUrl) {
        await uploadFileToAzure(typedAvatar, preSignedUrl)
          .then(() => {
            dispatch(
              updateUserProfileByPatchThunk({
                url: typedAvatar?.fileName || "",
              })
            );
          })
          .catch((error) => {
            console.error("Error uploading image:", error);
          });
      }
      return true;
    } catch (error) {
      console.error("Error in uploadProfileImage:", error);
      return false;
    }
  }

  useEffect(() => {
    setProfileName(user?.name ?? "");

    const fields: [keyof UserOnboarding, any][] = [
      ["name", user?.name || ""],
      ["email", user?.email || ""],
      ["phoneNumber", user?.phoneNumber || ""],
    ];

    fields.forEach(([name, value]) => {
      setValue(name, value);
    });
  }, [JSON.stringify(user), setValue]);

  useEffect(() => {
    async function signedImage() {
      if (authUser?.picture) {
        const randomNum = Math.floor(Math.random() * 100000);
        const fileName = `downloadedImage_${randomNum}.jpg`;
        await downloadImage(fileName);
      }
    }
    signedImage();
  }, [user]);

  const downloadImage = async (fileName: string) => {
    try {
      // Define path to save image
      const path = `${RNFS.DocumentDirectoryPath}/${fileName}`;

      // Download image
      const response = await RNFS.downloadFile({
        fromUrl: authUser?.picture,
        toFile: path,
      }).promise;

      if (response.statusCode === 200) {
        setProfileImage(`file://${path}` || "");
        setAvatar({
          uri: `file://${path}` || "",
          fileName: fileName,
          type: "image/jpg",
        });
        getPreSignedUrl(fileName || "")
          .then((response) => {
            setPreSignUrl(response || "");
          })
          .catch((error) => {
            console.error("Error getting pre-signed URL:", error);
          });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleImageEdit = () => {
    ImagePicker.launchImageLibrary({ mediaType: "photo" }, (response) => {
      if (response.didCancel) {
        return;
      }

      if (response.errorCode) {
        console.error("ImagePicker Error: ", response.errorMessage);
        return;
      }

      const selectedImage = response?.assets?.[0];
      if (selectedImage?.uri) {
        // Handle the selected image URI
        setProfileImage(selectedImage.uri || "");
        setAvatar(selectedImage);

        try {
          getPreSignedUrl(selectedImage?.fileName || "DefaultName.jpg")
            .then((response) => {
              setPreSignUrl(response || "");
            })
            .catch((error) => {
              console.error("Error getting pre-signed URL:", error);
            });
        } catch (error) {
          console.error("Error in handleImageEdit:", error);
        }
      }
    });
  };

  const renderUnit = () => {
    return (
      <Typography.B2 style={styles.unit}>
        {getPheAllowanceUnit(consumptionType)}
      </Typography.B2>
    );
  };

  const onConsumptionTabSelector = (type: string) => {
    setSelectedConsumption(type);
    dispatch(setConsumptionUnit(type as "Phe" | "Protein"));
    setTimeout(() => {
      trigger("pheAllowance");
    }, 0);
  };

  return (
    <SafeAreaView style={styles.container}>
      {isLoading ? (
        <Loading />
      ) : (
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={Common.rowJustifyCenter}>
            <Typography.H3 style={Common.textBold}>Your Profile</Typography.H3>
          </View>

          <View>
            <ProfileHeader
              name={profileName ? profileName : user?.name}
              imageUrl={profileImage}
              isEditing={true}
              onEditPress={handleImageEdit}
            />

            <InputField
              name="name"
              control={control}
              value={profileName}
              onChangeText={(text: string) => {
                setProfileName(text || "");
                setValue("name", text || "");
              }}
              label={t("name")}
              placeholder={t("enterYourName")}
            />

            <InputField
              name="email"
              keyboardType="email-address"
              control={control}
              label={t("email")}
              editable={!user?.email}
              placeholder={t("enterYourEmail")}
            />

            <InputField
              name="phoneNumber"
              control={control}
              keyboardType="phone-pad"
              label={t("phoneNumber")}
              editable={!user?.phoneNumber}
              placeholder={t("enterYourPhone")}
            />

            <Typography.B2 style={styles.labelText}>{t("iAmA")}</Typography.B2>
            <View style={{ width: "70%" }}>
              <TabSelector
                style={styles.tabBg}
                tabs={["Patient", "Care Giver"]}
                selectedTab={selectedTab}
                onTabPress={setSelectedTab}
              />
            </View>

            <Typography.B2 style={styles.labelText}>
              {t("trackConsumption")}
            </Typography.B2>
            <View style={{ width: "70%" }}>
              <TabSelector
                style={styles.tabBg}
                tabs={["Phe", "Protein"]}
                selectedTab={selectedConsumption}
                onTabPress={(type) => {
                  onConsumptionTabSelector(type);

                }}
              />
            </View>

            {/* phe allowance */}
            <InputField
              name="pheAllowance"
              control={control}
              RightChild={renderUnit()}
              label={t(getAllowanceLabel(consumptionType))}
              placeholder={t(getAllowancePlaceholder(consumptionType))}
              keyboardType="decimal-pad"
            />

            <SimplifiedDietToggle
              isToggleOn={isSimplifiedDiet}
              onToggle={() => setIsSimplifiedDiet(!isSimplifiedDiet)}
              disabled={false}
              loading={false}
            />

            <InputField
              name="agreedToTerms"
              control={control}
              label={t("agreeTermsAndConditions")}
              label2={t("termsAndConditions")}
              label2Press={handleTermsAndConditions}
              component={RNCheckbox}
              errorStyle={{ marginTop: -20, marginLeft: 32 }}
              trigger="onSelect"
            />

            <InputField
              name="subscribedToUpdates"
              control={control}
              label={t("futureUpdates")}
              component={RNCheckbox}
              trigger="onSelect"
            />

            <InputField
              name="dataUsageConsent"
              control={control}
              label={t("userDataInsights")}
              component={RNCheckbox}
              errorStyle={{ marginTop: -2, marginLeft: 32 }}
              trigger="onSelect"
            />
          </View>

          <Button.Yellow
            style={styles.btnContainer}
            onPress={handleSubmit(handleContinue)}
            activeOpacity={0.85}
          >
            <Typography.B2 style={styles.btnText}>Continue</Typography.B2>
          </Button.Yellow>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

export default UserConfiguration;
