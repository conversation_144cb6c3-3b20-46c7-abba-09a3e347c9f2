import { Fonts } from "@/constants";
import { IS_ANDROID, SCREEN_HEIGHT } from "@/theme/_config";
import { Theme } from "@/types/theme/theme";
import { SCREEN_WIDTH } from "@gorhom/bottom-sheet";
import { Platform } from "react-native";
import {
  ms,
  scale,
  ScaledSheet,
  verticalScale,
} from "react-native-size-matters";

export const getOnboardingStyle = (theme: Theme) =>
  ScaledSheet.create({
    container: {
      padding: ms(10),
      flex: 1,
    },
    welcomeContainer: {
      backgroundColor: theme.colors.welcomeBg,
      flex: 1,
      justifyContent: "space-around",
      alignItems: "center",
      borderRadius: ms(16),
      overflow: "hidden",
    },
    userthemeContainer: {
      alignItems: "center",
      width: "100%",
      height: "90%",
    },
    flex_center: {
      flexGrow: 1,
      justifyContent: "center",
      alignItems: "center",
      width: " 100%",
      height: SCREEN_HEIGHT,
      paddingVertical: SCREEN_HEIGHT * 0.0145,
    },
    contentContainer: {
      height: "100%",
      width: "100%",
    },
    titleContainer: {
      alignItems: "center",
    },
    logo: {
      marginTop: ms(80),
      alignSelf: "center",
    },
    btnContainer: {
      width: scale(245),
      height: verticalScale(35),
      alignSelf: "center",
      borderRadius: 20,
      color: theme.colors.buttonText,
    },
    appTitle: {
      color: theme.colors.yellow,
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    textContainer: {
      marginVertical: ms(24),
      width: scale(276),
      alignItems: "center",
      marginTop: ms(48),
      textAlign: "center",
    },
    textContainerMini: {
      marginVertical: ms(10),
      width: scale(276),
      alignItems: "center",
    },
    btnText: {
      color: theme.colors.buttonText,
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    videoContainer: {
      borderRadius: ms(16),
      height:
        SCREEN_HEIGHT < 800 && IS_ANDROID
          ? SCREEN_HEIGHT * 0.54
          : SCREEN_HEIGHT * 0.6,
      marginHorizontal: ms(10),
      overflow: "hidden",
      backgroundColor: theme.colors.tile_bg,
    },
    guidesContainer: {
      backgroundColor: theme.colors.welcomeBg,
      paddingVertical: ms(8),
      margin: ms(10),
      height:
        SCREEN_HEIGHT <= 640 ? SCREEN_HEIGHT * 0.33 : SCREEN_HEIGHT * 0.29,
      justifyContent: "center",
      borderRadius: ms(16),
      overflow: "hidden",
    },
    backgroundVideo: {
      height: "100%",
      borderRadius: ms(100),
    },
    primaryText: {
      color: theme.colors.yellow,
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    labelText: {
      color: theme.colors.textPrimary,
      marginBottom: 14,
      marginTop: 14,
    },
    pheLabelText: {
      color: theme.colors.textPrimary,
      marginBottom: 14,
      marginTop: 14,
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    skipAll: {
      marginTop: ms(8),
      marginBottom: ms(4),
    },
    unit: {
      color: theme.colors.textPrimaryYellow,
      fontFamily: Fonts.RALEWAY_MEDIUM,
    },
    settingsContainer: {
      marginTop: ms(36),
      marginBottom: ms(10),
      width: SCREEN_WIDTH - ms(20),
    },
    selectedButton: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    accordionContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      backgroundColor: theme.colors.tile_bg,
      borderBottomRightRadius: ms(12),
      borderBottomLeftRadius: ms(12),
      paddingVertical: ms(12),
      marginTop: ms(-10),
      marginBottom: ms(10),
      padding: ms(15),
    },
    modeButton: {
      borderRadius: ms(12),
      borderWidth: ms(1),
      alignItems: "center",
      justifyContent: "center",
      width: ms(90),
      borderColor: theme.colors.textPrimary,
    },
    selectedButtonText: {
      color: theme.colors.reverseBlack,
    },
    bottomButtonContainer: {
      marginTop: SCREEN_HEIGHT > 640 ? ms(142) : ms(52),
      paddingHorizontal: ms(16),
    },
    buttonOutlineText: {
      fontFamily: Fonts.RALEWAY_BOLD,
      lineHeight: ms(15.5),
    },
    tabBg: {
      backgroundColor: theme.colors.tile_bg,
    },
    disclamerContainer: {
      flex: 1,
      backgroundColor: theme.colors.tile_bg,
    },
    disclaimerlogo: {
      alignSelf: "center",
      position: "absolute",
      top: Platform.OS === "android" ? ms(40) : ms(60),
    },
    disclaimerTitleContainer: {
      alignItems: "center",
      marginTop: ms(5),
    },
    disclaimerTitle: {
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    disclaimerSubTitle: {
      width: scale(296),
      alignItems: "center",
      marginTop: ms(8),
      textAlign: "center",
      fontSize: ms(12),
      lineHeight: ms(16),
      marginBottom: ms(12),
    },
    disclaimerBtnContainer: {
      width: scale(245),
      height: verticalScale(35),
      alignSelf: "center",
      borderRadius: ms(12),
    },
    disclaimerScrollContainer: {
      paddingBottom: 0,
    },
    intentionOptionsTitle: {
      fontSize: ms(22),
      fontFamily: Fonts.RALEWAY_BOLD,
      marginTop: ms(30),
      textAlign: "center",
    },
    intentionOptionsBtnContainer: {
      color: theme.colors.buttonText,
      marginBottom: verticalScale(60),
      width: scale(245),
      height: verticalScale(35),
      alignSelf: "center",
      borderRadius: ms(12),
    },
    intentionOptionsFlatListView: {
      gap: ms(10),
      paddingHorizontal: ms(20),
      marginTop: ms(20),
    },
    intentionOptionBtnText: {
      color: theme.colors.intentionBlackColor,
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    safeAreaView: {
      backgroundColor: theme.colors.tile_bg,
    },
    optionsView: {
      paddingHorizontal: ms(20),
    },
    createAccountBtnConatiner: {
      width: scale(245),
      height: verticalScale(35),
      alignSelf: "center",
      borderRadius: ms(12),
      color: theme.colors.buttonText,
    },
    createAccountHeaderTitle: {
      fontFamily: Fonts.RALEWAY_BOLD,
      fontSize: ms(18),
      textAlign: "center",
    },
    createAccountScrollView: {
      flex: 1,
    },
    disclaimerBottomView: {
      position: "absolute",
      bottom: ms(55),
      width: "100%",
      alignItems: "center",
    },
    disclaimerBottomUpperView: {
      position: "relative",
    },
    modalHeaderStyle: {
      width: SCREEN_WIDTH * 0.6,
      marginTop: ms(-10),
    },
    backIcon: {
      position: "absolute",
      top: Platform.OS === "android" ? ms(40) : ms(60),
      alignSelf: "flex-start",
      marginLeft: ms(10),
    },
    errorView: {
      textAlign: "center",
      marginBottom: verticalScale(20),
      color: theme.colors.red,
    },
  });
