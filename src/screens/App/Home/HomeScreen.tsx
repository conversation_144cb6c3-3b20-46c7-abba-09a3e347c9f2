import dayjs from "dayjs";
import { useSelector } from "react-redux";
import RenderHTML from "react-native-render-html";
import DeviceInfo from "react-native-device-info";
import { ScrollView } from "react-native-gesture-handler";
import { useFocusEffect, useIsFocused, useRoute } from "@react-navigation/native";
import { AppState, View, useWindowDimensions } from "react-native";
import React, { useState, useEffect, useRef, useCallback } from "react";

import {
  fetchDailyTasks,
  selectDailyTasks,
  fetchWeeklyProgress,
  selectWeeklyProgress,
  clearTaskIdCollection,
  fetchRecentMoodLog,
} from "@/store/slices/dashboardSlice";
import { useTheme } from "@/theme";
import styles from "./HomeScreen.styles";
import { Header } from "@/components/molecules";
import { getFCMToken } from "@/hooks/useNotifee";
import { SafeScreen } from "@/components/template";
import { config, IS_ANDROID } from "@/theme/_config";
import Loading from "@/components/atoms/Loading/Loading";
import { store, useAppDispatch, useAppSelector } from "@/store";
import { PushNotification } from "@/types/schemas/PushNotification";
import { registerDeviceThunk } from "@/store/slices/notificationSlice";
import { useAnalytics, analyticsEventType } from "@/hooks/useAnalytics";
import GenericModal from "@/components/molecules/GenericModal/GenericModal";
import MoodSelector from "@/components/molecules/MoodSelector/MoodSelector";
import WeeklyCalendar from "@/components/molecules/WeeklyCalendarCard/WeeklyCalendarCard";
import LabsDashboardCard from "@/components/molecules/LabsDashboardCard/LabsDashboardCard";
import TaskDashboardCard from "@/components/molecules/TaskDashboardCard/TaskDashboardCard";
import { fetchMedicationCategories } from "@/store/slices/taskManager/taskManager.middleware";
import {
  fetchFeatureUpdate,
  fetchTimeZonesThunk,
  selectShowWhatsNewModal,
  setShowWhatsNewModal,
} from "@/store/slices/settingsSlice";
import TasksCompletedSummary from "@/components/molecules/TasksCompletedSummary/TasksCompletedSummary";
import PheConsumedDashboardCard from "@/components/molecules/PheConsumedDashboardCard/PheConsumedDashboardCard";
import { clearVersionHistory } from "@/utils/versionStorage";
import GlobalTimeZoneModal from "@/components/molecules/GlobalTimeZoneModal/GlobalTimeZoneModal";
import {
  selectShowTimeZoneModal,
  selectProfileTimeZone,
  hideTimeZoneModal,
} from "@/store/slices/settingsSlice";
import { useTimezone } from "@/hooks/useTimezone";
import { getTimeZoneOffsetDiffAdvanced } from "@/utils/timezoneUtils";

const HomeScreen = () => {
  const route = useRoute();
  const isFocused = useIsFocused();
  const { variant, colors } = useTheme();
  const dispatch = useAppDispatch();
  const showTimeZoneModal = useAppSelector(selectShowTimeZoneModal);
  const { initializeTimezone, checkTimezoneOnFocus, updateToNewTimeZone, keepCurrentTimeZone } = useTimezone();
  // --- Device time zone state (live updates) ---
  const [deviceTimeZone, setDeviceTimeZone] = useState(
    Intl.DateTimeFormat().resolvedOptions().timeZone
  );
  // --- Profile time zone from Redux ---
  const profileTimeZone = useAppSelector(selectProfileTimeZone);
  const timeZones = useAppSelector((state) => state.settings.timeZones);
  const userTimeZone = useAppSelector((state) => state.user.user?.timeZoneId);
  // Compute formatted time zone difference string using the new utility
  const timeZoneDifference = getTimeZoneOffsetDiffAdvanced(userTimeZone, deviceTimeZone,"").formatted;
  // Show modal only if deviceTimeZone !== profileTimeZone
  const shouldShowTimeZoneModal = deviceTimeZone !== profileTimeZone && showTimeZoneModal;
  const { setAnalyticsEvent } = useAnalytics();
  const tasks = useSelector(selectDailyTasks);
  const weeklyProgress = useSelector(selectWeeklyProgress);
  const [selectedDate, setSelectedDate] = useState(dayjs().toISOString());
  const showModal = useAppSelector(selectShowWhatsNewModal);
  const { width } = useWindowDimensions();

  const [showLoader, setShowLoader] = useState(true); // Show on first launch
  const appStateRef = useRef(AppState.currentState);
  const loaderTimeout = useRef<NodeJS.Timeout | null>(null);
  const featureUpdate = useAppSelector((state) => state.settings.featureUpdate);

  const handleClose = () => {
    dispatch(setShowWhatsNewModal(false));
  };

  const calculateWeekRange = (date: string | Date) => {
    const selectedDateObj = dayjs(date);
    return {
      fromDate: selectedDateObj
        .startOf("week")
        .format("YYYY-MM-DDT00:00:00.000"),
      toDate: selectedDateObj.endOf("week").format("YYYY-MM-DDT23:59:59.999"),
    };
  };

  const { isUserAlreadyLoggedIn } = useAppSelector(
    (state) => state.notification
  );

  // Register Notification Device Token
  useEffect(() => {
    if (!isUserAlreadyLoggedIn) {
      registerNotification();
    }
  }, []);

  // Fetch weekly progress whenever the selected date changes
  useEffect(() => {
    const { fromDate, toDate } = calculateWeekRange(selectedDate);
    store.dispatch(fetchWeeklyProgress({ fromDate, toDate }));
  }, [selectedDate]);

  useEffect(() => {
    dispatch(fetchMedicationCategories());
    dispatch(fetchFeatureUpdate());
    dispatch(fetchTimeZonesThunk());
  }, [dispatch]);

  // Initialize timezone when user data is available
  const user = useAppSelector((state) => state.user.user);
  useEffect(() => {
    if (user && user.timeZoneId !== undefined) {
      initializeTimezone();
    }
  }, [user?.timeZoneId, initializeTimezone]);

  // Fetch daily tasks whenever the selected date changes
  useEffect(() => {
    store.dispatch(clearTaskIdCollection());
    store.dispatch(
      fetchDailyTasks(
        dayjs(selectedDate).endOf("day").format("YYYY-MM-DDT00:00:00.000")
      )
    );
  }, [selectedDate]);

  // Fetch mood log whenever the selected date changes
  useEffect(() => {
    store.dispatch(fetchRecentMoodLog(selectedDate));
  }, [selectedDate]);

  async function registerNotification() {
    const uniqueId = await DeviceInfo.getUniqueId();
    const payload: PushNotification = {
      deviceId: uniqueId,
      platform: IS_ANDROID ? "gcm" : "apns",
      deviceToken: getFCMToken() || "",
      isEnabled: false,
    };
    store.dispatch(registerDeviceThunk(payload));
  }

  // Extract `todayProgress` from `weeklyProgress` based on `selectedDate`
  const selectedDateFormatted = dayjs(selectedDate).format("YYYY-MM-DD");
  const todayProgress = weeklyProgress.find(
    (progress) =>
      dayjs(progress.date).format("YYYY-MM-DD") === selectedDateFormatted
  );

  const handleDayPress = (day: string, index: number, isoDate: string) => {
    setSelectedDate(isoDate);
    setAnalyticsEvent(analyticsEventType.custom, {
      event: "dashboard_navigate_calendar",
      item_id: "dashboard_navigate_calendar",
      action: "User navigates calendar",
    });
  };

  useEffect(() => {
    // Handle app state changes
    const subscription = AppState.addEventListener("change", (nextAppState) => {
      const prevAppState = appStateRef.current;

      // Show loader only when app comes from background
      if (prevAppState === "background" && nextAppState === "active") {
        setShowLoader(true);
        loaderTimeout.current = setTimeout(() => {
          setShowLoader(false);
        }, 1000); // Adjust duration as needed
      }

      appStateRef.current = nextAppState;
    });

    return () => {
      subscription.remove();
      if (loaderTimeout.current) clearTimeout(loaderTimeout.current);
    };
  }, []);

  useEffect(() => {
    // Handle initial app launch
    const initialTimeout = setTimeout(() => {
      setShowLoader(false);
    }, 1000); // Adjust as needed

    return () => clearTimeout(initialTimeout);
  }, []);

  useEffect(() => {
    // Listen for app state changes to update device time zone
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === "active") {
        const newTz = Intl.DateTimeFormat().resolvedOptions().timeZone;
        setDeviceTimeZone(newTz);

        // Check if timezone changed and re-initialize if needed
        if (newTz !== profileTimeZone) {
          initializeTimezone();
        }
      }
    };
    const subscription = AppState.addEventListener("change", handleAppStateChange);
    return () => subscription.remove();
  }, [profileTimeZone]);

  // Check timezone every time HomeScreen comes into focus
  useFocusEffect(
    useCallback(() => {
      const currentDeviceTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

      // Update device timezone state
      setDeviceTimeZone(currentDeviceTimeZone);

      // Check if current device timezone is different from profile timezone
      if (currentDeviceTimeZone !== profileTimeZone && user?.timeZoneId) {
        console.log('🕐 HomeScreen Focus: Timezone changed', {
          currentDeviceTimeZone,
          profileTimeZone,
          userTimeZone: user.timeZoneId
        });

        // Use the specialized focus check method
        checkTimezoneOnFocus();
      }
    }, [profileTimeZone, user?.timeZoneId, checkTimezoneOnFocus])
  );

  if (showLoader) {
    return <Loading />;
  }

  return (
    <SafeScreen>
      <ScrollView>
        <View style={styles.headerContainer}>
          <Header
            title="Dashboard"
            showHamburgerMenu
            isDark={variant === "dark"}
          />
        </View>

        {/* ✅ Update `todayProgress` based on selected date */}
        <TasksCompletedSummary
          percentage={
            todayProgress?.percentage
              ? parseInt(todayProgress.percentage.toFixed(0), 10)
              : 0
          }
        />

        <View style={styles.contentContainer}>
          <WeeklyCalendar
            weeklyProgressPercentages={weeklyProgress}
            onDayPress={handleDayPress}
            canSelectFuture
            hasPercentages
          />
          <View style={styles.cardBigSpace} />

          <TaskDashboardCard
            tasks={tasks}
            selectedDate={dayjs(selectedDate).toDate()}
            onTaskToggle={() => {
              const { fromDate, toDate } = calculateWeekRange(selectedDate);
              store.dispatch(fetchWeeklyProgress({ fromDate, toDate }));
            }}
          />

          <View style={styles.cardSpace} />
          <MoodSelector selectedDate={selectedDate} />
          <View style={styles.cardSpace} />
          <View style={styles.footerContainer}>
            <LabsDashboardCard />
            <PheConsumedDashboardCard date={selectedDate} />
          </View>
        </View>
      </ScrollView>

      <GenericModal
        isVisible={showModal}
        onConfirm={handleClose}
        customHeader={
          featureUpdate?.title && (
            <RenderHTML
              contentWidth={width}
              source={{ html: featureUpdate.title }}
              baseStyle={{
                color: colors.textPrimary,
                fontSize: 18,
                textAlign: "center",
                fontWeight: "bold",
              }}
            />
          )
        }
        customBody={
          featureUpdate?.description ? (
            <RenderHTML
              contentWidth={width}
              source={{ html: featureUpdate.description }}
              baseStyle={{
                color: colors.textPrimary,
                fontSize: 15,
                textAlign: "center",
              }}
            />
          ) : (
            <></>
          )
        }
        confirmText="Got it"
        onCloseIcon={handleClose}
        bannerImageSource={
          featureUpdate?.pictureUrl
            ? { uri: featureUpdate.pictureUrl }
            : require("@/theme/assets/images/meal.png")
        }
        headerColor={config.colors.yellow}
        showWhatsNew
      />

      <GlobalTimeZoneModal
        isVisible={shouldShowTimeZoneModal && route?.name === 'HomeScreen' && isFocused}
        currentTimeZone={deviceTimeZone}
        profileTimeZone={userTimeZone}
        timeZoneDifference={timeZoneDifference}
        onKeepCurrent={() => keepCurrentTimeZone()}
        onUpdateNew={async () => {
          await updateToNewTimeZone(deviceTimeZone, true);
        }}
        onClose={() => dispatch(hideTimeZoneModal())}
        timeZones={timeZones}
      />
    </SafeScreen>
  );
};

export default HomeScreen;
