/** @format */

import React, { useRef, useState } from "react";
import { View, ScrollView, TouchableOpacity, StatusBar, Keyboard, Linking } from "react-native";
import { SafeScreen } from "@/components/template";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getAISuggestionsStyle from "./AISuggestion.style";
import { Button, Typography } from "@/components/atoms";
import AIListCard from "@/components/molecules/AISuggestionsComponents/AIListCard";
import { useNavigation } from "@react-navigation/native";
import Each from "@/components/atoms/Each/Each";
import SearchListInput, { SearchListInputRef } from "@/components/atoms/SearchListInput/SearchListInput";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  addItemToMatchFoods,
  clearSearch,
  removeMatchFood,
  replaceWithAlternative,
  selectDietTracker,
  selectFoodItemsByFrequency,
} from "@/store/slices/dietTrackerSlice";
import { useCameraPermission } from "react-native-vision-camera";
import GenericModal from "@/components/molecules/GenericModal/GenericModal";
import { isNonEmptyArray } from "@/utils/helpers";
import CameraModal from "@/components/molecules/CameraModal/CameraModal";
import ImagePreview from "@/components/molecules/AISuggestionsComponents/ImagePreview";
import ManualEntryModal from "@/components/molecules/ManualEntryModal/ManualEntryModal";
import EmptyList from "@/components/atoms/EmptyList/EmptyList";
import { getFoodById } from "@/services/api/dietTrackerAPI";
import { PortionConverter } from "@/utils/portions";
import Loading from "@/components/atoms/Loading/Loading";
import { useDisableDrawerSwipe } from "@/hooks/useDisableDrawerSwipe";

const AISuggestions = () => {
  const styles: any = useDynamicStyles(getAISuggestionsStyle);
  const navigation = useNavigation();
  const { requestPermission } = useCameraPermission();
  const [showPermissionModal, setShowPermissionModal] = useState<boolean>(false);
  const [isVisibleCamera, seIsVisibleCamera] = useState<boolean>(false);
  const searchResults = useAppSelector(selectFoodItemsByFrequency);
  const { matchFoods, analysisId } = useAppSelector(selectDietTracker);
  const [isManualEntry, setIsManualEntry] = useState(false);
  // const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [currentId, setCurrentId] = useState<string | null>(null);

  const dispatch = useAppDispatch();
  const searchInputRef = useRef<SearchListInputRef>(null);
  const [alternativeSearch, setAlternativeSearch] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLarge, setIsLarge] = useState<boolean>(false);
  // const [swappedIndices, setSwappedIndices] = useState<Set<number>>(new Set());
  const [swappedItems, setSwappedItems] = useState<Set<string>>(new Set());
  // const handleSwap = (index: number) => {
  //   setSwappedIndices((prev) => new Set(prev).add(index));
  // };
  // const resetSwapForIndex = (index: number) => {
  //   setSwappedIndices((prev) => {
  //     const newSet = new Set(prev);
  //     newSet.delete(index);
  //     return newSet;
  //   });
  // };
  const handleSwap = (id: string) => {
    setSwappedItems((prev) => new Set(prev).add(id));
  };

  const resetSwapForId = (id: string) => {
    setSwappedItems((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };
  useDisableDrawerSwipe();

  const checkPermission = async () => {
    Keyboard.dismiss();
    const isGranted = await requestPermission();
    if (!isGranted) {
      setShowPermissionModal(true);
    } else {
      seIsVisibleCamera(true);
    }
  };
  // const handleOnDelete = (index: any) => {
  //   dispatch(removeMatchFood(index));
  // };
  const handleOnDelete = (id: string) => {
    const index = matchFoods.findIndex((item: any) => item._localId === id);
    if (index !== -1) {
      dispatch(removeMatchFood(index));
    }
  };
  const handleSelect = async (item: any) => {
    try {
      Keyboard.dismiss();
      setIsLoading(true);
      const food = await getFoodById(
        item?.foodId || item?.id,
        parseFloat(item?.editQuantity) || 1,
        item?.editUnit,
        item?.editGramWeight || 0
      );
      const itemToAdd = {
        ...food,
        analysis_id: analysisId,
      };
      searchInputRef?.current?.reset();
      dispatch(addItemToMatchFoods(itemToAdd));
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.error(error, "error in on Select of main search");
    }
  };

  // const handleManualEntry = (item: any) => {
  //   const { isAlternative, ...rest } = item;
  //   if (!isAlternative) {
  //     const itemToAdd = {
  //       ...rest,
  //       analysis_id: analysisId,
  //     };
  //     dispatch(addItemToMatchFoods(itemToAdd));
  //   } else {
  //     const itemToAdd = {
  //       ...rest,
  //       analysis_id: analysisId,
  //     };
  //     dispatch(
  //       replaceWithAlternative({
  //         index: currentIndex,
  //         data: itemToAdd,
  //       })
  //     );
  //   }
  // };

  const handleManualEntry = (item: any) => {
    const { isAlternative, ...rest } = item;
    const itemToAdd = { ...rest, analysis_id: analysisId };

    if (!isAlternative) {
      dispatch(addItemToMatchFoods(itemToAdd));
    } else {
      const index = matchFoods.findIndex((i: any) => i._localId === currentId);
      if (index !== -1) {
        dispatch(
          replaceWithAlternative({
            index,
            data: itemToAdd,
          })
        );
      }
    }
  };

  const handleOnCancel = () => {
    navigation.goBack();
  };
  console.log(matchFoods,"food");
  return (
    <>
      <ImagePreview setIsLarge={setIsLarge} />

      {!isLarge && (
        <SafeScreen styles={isManualEntry ? null : isVisibleCamera ? null : isLarge ? null : styles.mainBackground}>
          {!isVisibleCamera && <StatusBar translucent backgroundColor="transparent" barStyle="light-content" />}
          <View style={{ flex: 1, position: "relative" }}>
            <View style={styles.headerContainer}>
              <Typography.H3 style={styles.headerTitle}>AI Suggestions</Typography.H3>
              <TouchableOpacity onPress={handleOnCancel} style={styles.leftItem}>
                <Typography.B2 style={styles.cancelText}>Cancel</Typography.B2>
              </TouchableOpacity>
              <View style={styles.rightItem} />
            </View>
            <ScrollView
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
              contentContainerStyle={styles.suggestionsList}
            >
              <View style={styles.contentContainer}>
                {isNonEmptyArray(matchFoods) ? (
                  <>
                    <Typography.B2 style={styles.somethingMissingText}>Something missing?</Typography.B2>
                    <SearchListInput
                      ref={searchInputRef}
                      searchResults={searchResults}
                      showTitle={false}
                      onSelectItem={(item) => {
                        handleSelect(item);
                      }}
                      placeholder="Search for Foods"
                      onAddNew={() => {
                        setIsManualEntry(true);
                        setAlternativeSearch(false);
                      }}
                    />
                    <View style={{ zIndex: -1 }}>
                      <Typography.B2 style={styles.aiSuggestionsTitle}>AI Suggestions</Typography.B2>
                      {/* <Each
                        of={matchFoods}
                        render={(item, index) => (
                          <AIListCard
                            key={item?._localId}
                            index={index}
                            onDelete={() => {
                              handleOnDelete(index);
                              resetSwapForIndex(index);
                            }}
                            item={item}
                            onAddNew={() => {
                              setIsManualEntry(true);
                              setAlternativeSearch(true);
                              setCurrentIndex(index);
                            }}
                            isSwapped={swappedIndices.has(index)}
                            onSwap={() => handleSwap(index)}
                            resetSwap={() => resetSwapForIndex(index)}
                          />
                        )}
                      /> */}
                      <Each
                        of={matchFoods}
                        render={(item) => (
                          <AIListCard
                            key={`${item._localId}-${item._version || 0}`}
                            index={matchFoods.findIndex((f) => f._localId === item._localId)} // if AIListCard still needs index
                            onDelete={() => {
                              handleOnDelete(item._localId);
                              resetSwapForId(item._localId);
                            }}
                            item={item}
                            onAddNew={() => {
                              setIsManualEntry(true);
                              setAlternativeSearch(true);
                              setCurrentId(item._localId); // now storing ID
                            }}
                            isSwapped={swappedItems.has(item._localId)}
                            onSwap={() => handleSwap(item._localId)}
                            resetSwap={() => resetSwapForId(item._localId)}
                          />
                        )}
                      />
                    </View>
                  </>
                ) : (
                  <>
                    <SearchListInput
                      ref={searchInputRef}
                      searchResults={searchResults}
                      showTitle={false}
                      placeholder="Search for Foods"
                      onSelectItem={(item) => {
                        handleSelect(item);
                      }}
                      onAddNew={() => {
                        setIsManualEntry(true);
                        setAlternativeSearch(false);
                      }}
                    />
                    <EmptyList style={{ zIndex: -1 }} text="No items to display" />
                  </>
                )}
              </View>
              <View style={styles.bottomButtonsContainer}>
                <Button.YellowOutline onPress={checkPermission} style={styles.buttons}>
                  <Typography.B1 style={styles.buttonsText}>Retake Photo</Typography.B1>
                </Button.YellowOutline>
                <Button.Main
                  disabled={!isNonEmptyArray(matchFoods)}
                  onPress={() => navigation.navigate("LogFood" as never)}
                  style={styles.buttons}
                >
                  <Typography.B1 style={{ ...styles.buttonsText, ...styles.continueText }}>Continue</Typography.B1>
                </Button.Main>
              </View>
            </ScrollView>
          </View>

          <GenericModal
            isVisible={showPermissionModal}
            onClose={() => setShowPermissionModal(false)}
            onConfirm={() => Linking.openSettings()}
            headerText="Camera Permission Required"
            bodyText="To access the camera, please grant permission in your device settings."
            confirmText="Enable"
            closeText="Cancel"
            customHeader={null}
            customBody={null}
            customFooter={null}
          />
          <CameraModal
            isVisible={isVisibleCamera}
            onClose={() => {
              seIsVisibleCamera(false);
            }}
          />
          <ManualEntryModal
            isVisible={isManualEntry}
            onClose={() => {
              setIsManualEntry(false);
              dispatch(clearSearch());
              searchInputRef.current?.reset();
              setAlternativeSearch(false);
            }}
            setPayLoadForEntry={(val) => handleManualEntry(val)}
            isAlternative={alternativeSearch}
          />
        </SafeScreen>
      )}

      {isLoading && <Loading />}
    </>
  );
};

export default AISuggestions;
