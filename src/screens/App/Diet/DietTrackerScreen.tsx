/** @format */

import { useFocusEffect, useNavigation } from "@react-navigation/native";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Keyboard,
  ScrollView,
  TouchableOpacity,
  View,
} from "react-native";
import { ms } from "react-native-size-matters";
import { useSelector } from "react-redux";

import { Button, Typography } from "@/components/atoms";
import Card from "@/components/atoms/Card/Card";
import TabSelector from "@/components/atoms/TabSelector/TabSelector";
import Header from "@/components/molecules/Header/Header";
import LogFoodBottomSheet from "@/components/molecules/LogFoodBottomSheet/LogFoodBottomSheet";
import MealItem from "@/components/molecules/MealItem/MealItem";
import PheAllowanceCard from "@/components/molecules/PheAllowanceCard/PheAllowanceCard";
import SaveMealModal from "@/components/molecules/SaveMealModal/SaveMealModal";
import WeeklyCalendar from "@/components/molecules/WeeklyCalendarCard/WeeklyCalendarCard";
import { SafeScreen } from "@/components/template";
import { analyticsEventType, useAnalytics } from "@/hooks/useAnalytics";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  deleteFoodEntry,
  fetchFoodEntries,
  saveMeal,
  selectDietDate,
  selectDietTracker,
  selectDietTrackerLoading,
  selectFoodEntries,
  selectTimeSlot,
} from "@/store/slices/dietTrackerSlice"; // Redux slice action
import {
  fetchDailyConsumedPheAllowance,
  selectDailyPheAllowance,
} from "@/store/slices/pheAllowanceSlice";
import Common from "@/theme/common.style";
import { FoodItemTransformer } from "@/utils/diet";
import { PortionConverter } from "@/utils/portions";
import Icons from "@/theme/assets/images/svgs/icons";
import {
  getPheAllowanceUnit,
  getPheAllowanceValue,
  removeTrailingZeros,
  roundNumber,
  updateDateWithTimeSlot,
} from "@/utils/helpers";
import { FoodEntryItem } from "@/types/schemas/dietTracker";
import dayjs from "dayjs";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getDietTrackStyles from "./DietTrackerScreen.style";
import { useTheme } from "@/theme";
import {
  selectConsumptionUnit,
  selectIsSimplifiedDiet,
} from "@/store/slices/settingsSlice";
import LogFoodInputModal from "@/components/molecules/LogFoodInputModal/LogFoodInputModal";

const DietTrackerScreen: React.FC = () => {
  const [isLogFoodVisible, setLogFoodVisible] = useState(false);
  const [isLogFoodInputVisible, setIsLogFoodInputVisible] = useState(false);
  const [isSaveMealVisible, setSaveMealVisible] = useState(false);
  const [selectedFood, setSelectedFood] = useState<FoodEntryItem[]>([]);
  const { setAnalyticsEvent } = useAnalytics();
  const consumptionType = useSelector(selectConsumptionUnit);

  const styles: any = useDynamicStyles(getDietTrackStyles);

  const [foodsData, setFoodsData] = useState<{
    items?: any[];
    phe?: any;
    mealExists?: boolean;
    mealFoodId?: string;
    consumedPhe?: string;
  }>({});
  const dailyPheAllowance = useSelector(selectDailyPheAllowance);

  const { user } = useAppSelector((state) => state);
  const dispatch = useAppDispatch();
  const { colors, variant } = useTheme();
  const navigation = useNavigation();
  const { selectedDietDate, selectedTimeSlot, status } =
    useAppSelector(selectDietTracker);
  const isSimplifiedDiet = useAppSelector(selectIsSimplifiedDiet);
  // console.log({foodsData, selectedTimeSlot, date: Date.now()})

  // Select food entries from Redux store
  const foodEntries = useSelector(selectFoodEntries) || [];
  const isLoading = useSelector(selectDietTrackerLoading);

  const fetchEntries = useCallback(async () => {
    const formattedDate = new Date(selectedDietDate).toISOString(); // Extract the date part in YYYY-MM-DD format

    dispatch(fetchFoodEntries({ date: formattedDate }));

    await dispatch(
      fetchDailyConsumedPheAllowance(
        dayjs(formattedDate).endOf("day").format("YYYY-MM-DDT23:59:59.999")
      )
    );
  }, [selectedDietDate, dailyPheAllowance, dispatch]);

  useFocusEffect(
    useCallback(() => {
      if (selectedDietDate) {
        fetchEntries();
      }
    }, [selectedDietDate, dispatch, fetchEntries, dailyPheAllowance])
  );

  useEffect(() => {
    const transformer = new FoodItemTransformer(foodEntries);

    const categorizedFoodItems = transformer.categorizeFoodItems();
    const totalPHEByCategory =
      transformer.calculateTotalPHEAndProteinFromItemsByCategory(
        isSimplifiedDiet
      );
    const mealExistenceByCategory = transformer.checkMealExistenceByCategory();
    const propName = consumptionType === "Protein" ? "protein" : "phe";
    setFoodsData({
      items: categorizedFoodItems[selectedTimeSlot],
      phe: totalPHEByCategory[selectedTimeSlot]?.[propName],
      mealExists: mealExistenceByCategory[selectedTimeSlot]?.exists,
      mealFoodId: mealExistenceByCategory[selectedTimeSlot]?._id,
      consumedPhe: transformer
        .calculateTotalPHEAndProteinFromItems(
          undefined,
          undefined,
          isSimplifiedDiet
        )
        ?.[propName]?.total?.toFixed(2),
    });
  }, [
    JSON.stringify(foodEntries),
    selectedTimeSlot,
    selectedDietDate,
    consumptionType,
    isSimplifiedDiet,
  ]);

  const handleLogFood = async () => {
    setAnalyticsEvent(analyticsEventType.custom, {
      item_id: "diet_custom_food_started",
      event: "diet_custom_food_started",
      action: "User tapped custom food",
    });
    setIsLogFoodInputVisible(false);
    await dispatch(
      fetchFoodEntries({ date: new Date(selectedDietDate).toISOString() })
    );
  };

  const handleSaveMeal = async (mealName: string) => {
    try {
      setAnalyticsEvent(analyticsEventType.custom, {
        event: "diet_create_meal_started",
        item_id: "diet_create_meal_started",
        action: "User tapped create meal",
      });
      const time = updateDateWithTimeSlot(
        `${selectedDietDate}`,
        selectedTimeSlot
      );
      const foodInstance = new FoodItemTransformer(foodEntries);

      // Calculate Quantity and Unit
      const { totalQuantity, unit } =
        foodInstance.calculateTotalQuantityByTimeSlot()?.[selectedTimeSlot] ||
        {};

      // Calculate total phe and protein from all food items
      const { totalPhe, totalProtein, totalGramWeight } =
        foodsData?.items?.reduce(
          (totals, food) => {
            food.items?.forEach((item: any) => {
              totals.totalPhe += parseInt(item.phe) || 0;
              totals.totalProtein += parseInt(item.protein) || 0;
              totals.totalGramWeight += parseInt(item.gram_weight) || 0;
            });
            return totals;
          },
          { totalPhe: 0, totalProtein: 0, totalGramWeight: 0 }
        );

      // return
      // Map foodsData.items to construct the payload's ingredients field
      // Construct ingredients only from foods with exactly one item
      const ingredients = foodsData.items
        ?.filter((food) => food.items?.length === 1) // Include only foods with one item
        .flatMap(
          (food) =>
            food.items?.map((item: any) => {
              return {
                description: item.description,
                category: "", // Default category if not provided
                quantity: item.quantity || 0,
                unit: item.unit || "",
                food_id: item.food_id, // Include food_id from items
                nutrients: [
                  {
                    name: "phe",
                    amount: item.phe || 0,
                    unit: "mg",
                  },
                  {
                    name: "protein",
                    amount: item.protein || 0,
                    unit: "g",
                  },
                ],
                user_flags: {
                  is_free: !!item.isFreeFood,
                },
                gram_weight: item.gram_weight,
              };
            }) || []
        );

      // Construct Payload for saveMeal
      const createFoodPayload = {
        description: mealName,
        category: "Meal",
        quantity: 1,
        unit: unit,
        time: time,
        nutrients: [
          {
            name: "phe",
            amount: parseFloat(totalPhe),
            unit: "mg",
          },
          {
            name: "protein",
            amount: parseFloat(totalProtein),
            unit: "g",
          },
        ],
        ingredients: ingredients,
        portions: [
          {
            name: unit,
            quantity: 1,
            gram_weight: totalGramWeight,
          },
        ],
        user_flags: {
          is_free: false,
        },
      };

      // Example: IDs of food entries to delete (replace with real IDs)
      const foodEntriesToDelete = foodsData?.items
        ?.filter((food) => food.items?.length <= 1) // Only include foods with more than one item
        ?.map((food) => food._id); // Extract the _id of those foods

      Keyboard.dismiss();

      // Dispatch saveMeal action
      const result = await dispatch(
        saveMeal({ createFoodPayload, foodEntriesToDelete })
      );

      if (saveMeal.fulfilled.match(result)) {
        setAnalyticsEvent(analyticsEventType.custom, {
          event: "diet_create_meal_logged",
          item_id: "diet_create_meal_logged",
          action: "User logged created meal",
        });
        setAnalyticsEvent(analyticsEventType.custom, {
          event: "diet_meal_saved",
          item_id: "diet_meal_saved",
          action: "User saved meal from diet tracker view",
        });
        fetchEntries();
        setSaveMealVisible(false);
      } else {
        console.error("Failed to save meal:", result.payload);
      }
    } catch (error) {
      console.error("Error saving meal:", error);
    } finally {
      setSaveMealVisible(false);
      setIsLogFoodInputVisible(false);
    }
  };

  const handleDelete = async (entryId: string) => {
    if (!entryId) {
      console.error("No valid entryId provided for deletion");
      return;
    }

    const result = await dispatch(deleteFoodEntry(entryId));
    if (deleteFoodEntry.fulfilled.match(result)) {
      await dispatch(
        fetchFoodEntries({ date: new Date(selectedDietDate).toISOString() })
      );
      setIsLogFoodInputVisible(false);
    } else {
      console.error("Failed to delete food entry:", result.payload);
    }
  };

  const handleTabPress = (tab: string) => {
    dispatch(selectTimeSlot(tab?.toLowerCase() || "morning"));
  };

  const MealandFoodsList = useCallback(() => {
    if (!foodsData?.items?.length) {
      return (
        <View style={styles.emptyMealView}>
          <Typography.B2>No food logged for {selectedTimeSlot}</Typography.B2>
        </View>
      );
    }

    // Helper function to calculate meal values based on current toggle state
    const calculateMealValue = (
      food: any,
      propName: string,
      isSimplified: boolean
    ) => {
      if (!food?.items || food?.items.length === 0) {
        return food?.[propName] || 0;
      }

      return food?.items?.reduce((total: number, item: any) => {
        // If Simplified Diet is enabled and the item is marked as free, exclude it
        if (isSimplified && item?.isFreeFood) {
          return total;
        }
        // Otherwise, include the item's value
        return total + (Number(item?.[propName]) || 0);
      }, 0);
    };

    return React.Children.toArray(
      foodsData?.items
        ?.slice() // Create a copy to avoid mutating the original array
        ?.sort(
          (a: any, b: any) => dayjs(a.time).valueOf() - dayjs(b.time).valueOf()
        ) // Sort using Day.js
        ?.map((food: any, index: number) => {
          const key = `${food?.items?.[0]?.food_id}+${food?.items?.[0]?.quantity}+${food?.items?.[0]?.protein}+${food?.items?.[0]?.phe}`;

          const onPress = () => {
            setSelectedFood({
              entryId: food._id,
              ...(food || {}),
              gram_weight: PortionConverter.toGrams(
                food?.unit,
                food?.quantity
              )?.toFixed(2),
              selectedFromList: true,
              editQuantity: food?.quantity || 1,
              editUnit: food?.unit || "",
              editGramWeight: food?.gram_weight,
              isFreeFood: food?.items?.[0]?.isFreeFood,
            });

            setIsLogFoodInputVisible(true);
          };

          // Check if there is more than one item
          const isMultipleItems = food?.items?.length > 1;
          const propName = consumptionType === "Protein" ? "protein" : "phe";
          const mealValue = calculateMealValue(
            food,
            propName,
            isSimplifiedDiet
          );
          // const simplifiedMealValues = isSimplifiedDiet ? food?.items?.reduce((total: number, item: { [key: string]: any }) => {
          //   if (item?.isFreeFood) {
          //     return total;
          //   }
          //   return total + (Number(item?.[propName]) || 0);
          // }, 0)
          //   :
          //   food?.[propName];

          if (isMultipleItems) {
            return (
              <View style={{ borderRadius: 8, overflow: "hidden" }}>
                <MealItem
                  onPress={onPress}
                  name={food.description} // Food description
                  quantity={`${food?.quantity || 0}`} // Total quantity with unit
                  phe={`${removeTrailingZeros(roundNumber(mealValue)) || 0}`}
                  icon={<Icons.Chevron color={colors.textPrimary} />}
                  unit={food?.unit || "g"}
                >
                  {React.Children.toArray(
                    food.items.map((item: any, i: number) => {
                      return (
                        <MealItem
                          style={styles.border_0}
                          textStyle={styles.foodItemText}
                          name={item.description} // Food description
                          quantity={`${removeTrailingZeros(item.quantity) || 0}`} // Quantity with unit
                          phe={`${roundNumber(item?.[propName])}`}
                          icon={<View style={styles.tagSpacing} />}
                          unit={item.unit}
                          isFreeFood={item?.isFreeFood}
                        />
                      );
                    })
                  )}
                </MealItem>
              </View>
            );
          } else {
            // Single item display
            return (
              <MealItem
                key={key + index}
                onPress={() => {
                  setSelectedFood({
                    entryId: food._id,
                    description: food.description,
                    quantity: food?.items?.[0]?.quantity || 0,
                    protein: food?.items?.[0]?.protein || 0,
                    phe: food?.items?.[0]?.phe || "0",
                    unit: food?.items?.[0]?.unit || "g",
                    food_id: food?.items?.[0]?.food_id,
                    selectedFromList: true,
                    editQuantity: food?.items?.[0]?.quantity || 1,
                    editUnit: food?.items?.[0]?.unit || "",
                    editGramWeight: food?.items?.[0]?.gram_weight,
                    isFreeFood: food?.items?.[0]?.isFreeFood,
                  }); // Store entry._id along with food
                  setIsLogFoodInputVisible(true);
                }}
                name={food.description} // Food description
                quantity={`${removeTrailingZeros(food?.items?.[0]?.quantity) || 0}`}
                unit={food?.items?.[0]?.unit || "g"}
                phe={`${removeTrailingZeros(roundNumber(food?.items?.[0]?.[propName])) || 0}`}
                icon={<View style={styles.tagSpacing} />}
                isFreeFood={food?.items?.[0]?.isFreeFood}
              />
            );
          }
        })
    );
  }, [JSON.stringify(foodsData), selectedTimeSlot, consumptionType]);

  const fallbackValue = `0 ${consumptionType === "Protein" ? " g" : " mg"}`;

  return (
    <SafeScreen>
      <View style={styles.headerView}>
        <Header
          title="Diet Tracker"
          showHamburgerMenu
          isDark={variant === "dark"}
        />
      </View>

      <View style={styles.container}>
        <ScrollView
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
        >
          <WeeklyCalendar
            onDayPress={(day, index, date) => {
              setAnalyticsEvent(analyticsEventType.custom, {
                event: "diet_navigate_calendar",
                item_id: "diet_navigate_calendar",
                action: "User navigates calendar",
              });
              dispatch(selectDietDate(date)); // Update the selected date
            }}
            isPeriod
          />

          <PheAllowanceCard
            key={dailyPheAllowance + (foodsData?.consumedPhe || "")}
            consumedPHELevel={
              foodsData?.consumedPhe
                ? Math.round(Number(foodsData.consumedPhe)) +
                  `${consumptionType === "Protein" ? " g" : " mg"}`
                : fallbackValue
            }
            dailyPHEAllowance={
              dailyPheAllowance
                ? getPheAllowanceValue(dailyPheAllowance, consumptionType) +
                  `${consumptionType === "Protein" ? " g" : " mg"}`
                : fallbackValue
            }
          />

          {/* Meal List */}
          <Card style={styles.mealListView}>
            <View style={styles.tabSelectorContainer}>
              {/* Tabs */}
              <View style={{ width: "72%" }}>
                <TabSelector
                  tabs={["morning", "afternoon", "evening"]}
                  selectedTab={selectedTimeSlot}
                  onTabPress={handleTabPress}
                />
              </View>

              <Button.Outline
                onPress={() => {
                  // setIsLogFoodInputVisible(false);
                  // setSelectedFood(undefined);
                  // setLogFoodVisible(true);
                  // navigation.navigate("AISuggestions" as never);
                  navigation.navigate("LogFoodMain" as never);
                }}
                style={styles.addButton}
              >
                <View style={styles.iconTextWrapper}>
                  <Icons.Plus color={colors.textPrimary} />
                  <Typography.B1 style={[Common.textBold, styles.textButton]}>
                    Add
                  </Typography.B1>
                </View>
              </Button.Outline>
            </View>
            {/* Total Phe */}
            <Typography.B1 style={[Common.textBold, styles.totalPheText]}>
              Total {consumptionType === "Protein" ? "PRO" : "Phe"}:{" "}
              {removeTrailingZeros(roundNumber(foodsData?.phe?.total)) || 0} {getPheAllowanceUnit(consumptionType)}
              {/* {foodsData?.phe?.unit} */}
            </Typography.B1>

            {isLoading ? (
              <View style={styles.emptyMealView}>
                <ActivityIndicator size="large" color={colors.primary} />
              </View>
            ) : (
              <View style={styles.mealList}>
                <View style={{ gap: ms(5) }}>{MealandFoodsList()}</View>
                {(foodsData?.items || []).filter(
                  (food) => food?.items?.length === 1
                ).length > 1 &&
                  !foodsData?.mealExists &&
                  !foodsData?.items?.some(
                    (food) => food?.items?.length > 1
                  ) && ( // Check if there are foods with more than one item
                    <TouchableOpacity
                      style={styles.saveMealContainer}
                      onPress={() => setSaveMealVisible(true)}
                    >
                      <View style={styles.saveMealRow}>
                        <Icons.SaveArrow
                          style={styles.saveIcon}
                          color={colors.textPrimaryYellow}
                        />
                        <Typography.B2
                          style={[Common.textBold, styles.saveMealText]}
                        >
                          Save as Meal
                        </Typography.B2>
                      </View>
                    </TouchableOpacity>
                  )}
              </View>
            )}

            {/* Meal Items */}
          </Card>
        </ScrollView>
      </View>

      <LogFoodBottomSheet
        isVisible={isLogFoodVisible}
        onClose={() => {
          setLogFoodVisible(false);
        }}
        onCloseFromInside={() => {
          setLogFoodVisible(false);
        }}
        userName={
          user?.user?.name ? user.user.name.split(" ")[0] : user?.user?.name
        }
      />
      <LogFoodInputModal
        selectedFood={selectedFood}
        isVisible={isLogFoodInputVisible}
        onClose={() => {
          setIsLogFoodInputVisible(false);
        }}
        onDeleteFood={() => {
          handleDelete(selectedFood.entryId);
        }}
        onLogFood={handleLogFood}
        isUpdating
      />
      <SaveMealModal
        isVisible={isSaveMealVisible}
        onClose={() => setSaveMealVisible(false)}
        onSave={handleSaveMeal}
      />
    </SafeScreen>
  );
};

export default DietTrackerScreen;
