import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { ms } from "react-native-size-matters";
import { CurveType } from "gifted-charts-core";
import React, { useCallback, useEffect, useState } from "react";
import { Line<PERSON>hart } from "react-native-gifted-charts";
import { LinearGradient, Stop } from "react-native-svg";
import { ScrollView as GScrollView } from "react-native-gesture-handler";
import {
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from "react-native";

import {
  submitLabEntry,
  updateLabResult,
  deleteLabResult,
  fetchLabPheTrends,
  fetchRecentPheResults,
  selectRecentPheResults,
  selectLabs,
  selectDateRange,
  resetDateRange,
} from "@/store/slices/labsSlice";
import Common from "@/theme/common.style";
import { useAppDispatch, useAppSelector } from "@/store";
import { DateRangeLabel, LabPayload } from "@/types/schemas/labs";
import { SafeScreen } from "@/components/template";
import { formatDateMMDDYYYY } from "@/utils/helpers";
import Icons from "@/theme/assets/images/svgs/icons";
import { Button, Card, Typography, Dropdown } from "@/components/atoms";
import {
  Header,
  PheResultBottomSheet,
  SelectDateBottomSheet,
} from "@/components/molecules";
import { analyticsEventType, useAnalytics } from "@/hooks/useAnalytics";
import { useTheme } from "@/theme";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getLabsStyles from "./LabsScreen.styles";
import rangeDropDownData from "@/constants/lab";
import { getDateRangeFromLabel } from "@/utils/lab";
import { useFocusEffect } from "@react-navigation/native";

const LabScreen: React.FC = () => {
  const [lineData, setLineData] = useState([]);
  const [maxValue, setMaxValue] = useState(0);

  const [status, setStatus] = useState({
    recentPhe: "idle",
    graphTrends: "idle",
  });

  const { colors, variant } = useTheme();
  const dispatch = useAppDispatch();

  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [isPheResultFormVisible, setIsPheResultFormVisible] = useState(false);
  const [isSelectDateVisible, setIsSelectDateVisible] = useState(false);
  const [selectedPheResult, setSelectedPheResult] = useState<any>(null);
  const [mode, setMode] = useState<"add" | "edit">("add");
  const [labData, setLabData] = useState([]); // State to store fetched lab data
  const [metric, setMetric] = useState("mg/dl");
  const { width: SCREEN_WIDTH } = Dimensions.get("window");
  const { setAnalyticsEvent } = useAnalytics();
  const [rangeDropdownVisible, setRangeDropdownVisible] = useState(false);
  const [selectedRange, setSelectedRange] = useState<DateRangeLabel>(DateRangeLabel.Last90Days);

  const styles: any = useDynamicStyles(getLabsStyles);
  const { dateRange } = useAppSelector(selectLabs);

  const dropdownData = [
    { id: 1, text: "mg/dl" },
    { id: 2, text: "μmol/l" },
  ];

  const recentResults = useSelector(selectRecentPheResults);

   useFocusEffect(
    useCallback(() => {
      return () => {
        dispatch(resetDateRange());
        setSelectedRange(DateRangeLabel.Last90Days)
      };
    }, [dispatch])
  );

  useEffect(() => {
    const { fromDate, toDate } = dateRange;
    if (fromDate && toDate) {
      handleFetchGraphData(fromDate, toDate);
    }
  }, [JSON.stringify(dateRange)]);

  // Fetch initial data
  useEffect(() => {
    dispatch(fetchRecentPheResults());
  }, [dispatch]);

  const handleFetchGraphData = async (fromDate, toDate) => {
    setStatus((prev) => ({
      ...prev,
      graphTrends: "loading",
    }));

    try {
      const response = await dispatch(
        fetchLabPheTrends({ fromDate, toDate })
      ).unwrap();

      setStatus((prev) => ({
        ...prev,
        graphTrends: "success",
      }));

      setLabData(response);
      updateLineChart(response, metric);
    } catch (error) {
      setStatus({
        recentPhe: "error",
        graphTrends: "error",
      });
      setLabData([]);
      setLineData([]);
    }
  };

  const handleResultPress = (item: any) => {
    setSelectedPheResult(item);
    setMode("edit");
    setIsPheResultFormVisible(true);
  };

  const handleAddPress = () => {
    setSelectedPheResult(null);
    setMode("add");
    setIsPheResultFormVisible(true);
  };

  const handleDateSave = async ({ fromDate, toDate }) => {
    dispatch(selectDateRange({ fromDate, toDate })); // Update Redux state
    await handleFetchGraphData(fromDate, toDate); // Fetch graph data using selected dates
  };

  const updateLineChart = (
    data: {
      sampleDate: string;
      pheMilligramPerDeciliter: number;
      pheMicromolePerLiter: number;
    }[],
    selectedMetric: string
  ) => {
    if (!data.length) return;

    const sortedData = [...data].sort(
      (a, b) =>
        new Date(a.sampleDate).getTime() - new Date(b.sampleDate).getTime()
    );

    const transformedData = [];

    const getDaysBetween = (start: string, end: string) => {
      const startDate = dayjs(start);
      const endDate = dayjs(end);
      return endDate.diff(startDate, "day");
    };

    for (let i = 0; i < sortedData.length; i++) {
      const item = sortedData[i];
      const value =
        selectedMetric === "mg/dl"
          ? item.pheMilligramPerDeciliter
          : item.pheMicromolePerLiter;

      // Push real data point
      transformedData.push({
        value: parseFloat(value.toFixed(2)),
        label: dayjs(item.sampleDate).format("MM/DD"),
        hideDataPoint: false,
      });

      // Add spacer points (simulate spacing), only if dayDiff > 1
      if (i < sortedData.length - 1) {
        const currentDate = item.sampleDate;
        const nextDate = sortedData[i + 1].sampleDate;
        const dayDiff = getDaysBetween(currentDate, nextDate);

        if (dayDiff > 1) {
          let spacersToInsert = 1;
          if (dayDiff <= 7) {
            spacersToInsert = 1; // Small gap
          } else if (dayDiff <= 14) {
            spacersToInsert = 2; // Medium gap
          } else {
            spacersToInsert = 3; // Large gap
          }

          for (let j = 0; j < spacersToInsert; j++) {
            transformedData.push({
              value: null, // Prevent line drawing
              label: "",
              hideDataPoint: true,
            });
          }
        }
      }
    }

    // Calculate max value from real points only
    const maxVal = Math.max(
      ...transformedData
        .filter((item) => item.value !== null)
        .map((item) => item.value!)
    );

    setLineData(transformedData);
    setMaxValue(maxVal);
  };

  // Handle metric change
  const handleMetricChange = (selectedMetric) => {
    setMetric(selectedMetric);
    updateLineChart(labData, selectedMetric); // Update line chart using fetched lab data
  };

  const onRangeSelector = (selectedRange: string) => {
    setSelectedRange(selectedRange as DateRangeLabel);
    const range = getDateRangeFromLabel(selectedRange);
    if (selectedRange === DateRangeLabel.Custom || range === null) {
      setIsSelectDateVisible(true);
    } else {
      handleDateSave(range);
    }
  };

  const handlePartialLabEvent = (data: {
    sampleDate: string;
    testDate: string;
  }) => {
    if (!data.sampleDate || !data.testDate) {
      // setAnalyticsEvent(analyticsEventType.custom, {
      //   event: "labs_partial_result_added",
      //   item_id: "labs_partial_result_added",
      //   action: "User added partial labs",
      // });
    }
  };

  const handleSubmit = async (data: {
    pheLevel: number;
    pheMetrics: number;
    sampleDate: string;
    testDate: string;
  }) => {
    try {
      handlePartialLabEvent(data);
      await dispatch(
        submitLabEntry({
          pheLevel: data.pheLevel,
          pheMetrics: data.pheMetrics,
          testDate: data.testDate,
          sampleDate: data.sampleDate,
        })
      )
        .unwrap()
        .then(() => {
          dispatch(fetchRecentPheResults());
          handleFetchGraphData(dateRange.fromDate, dateRange.toDate); // Corrected
          setIsPheResultFormVisible(false);
          setAnalyticsEvent(analyticsEventType.custom, {
            event: "labs_result_added",
            item_id: "labs_result_added",
            action: "User added full lab result",
          });
        })
        .catch((error) => {
          console.error("Error adding lab entry:", error);
        });
    } catch (error: any) {
      console.error("Error adding lab entry:", error);
    }
  };

  const handleUpdate = async (data: LabPayload) => {
    setStatus((prev) => ({
      ...prev,
      recentPhe: "loading",
    }));
    try {
      await dispatch(updateLabResult(data))
        .unwrap()
        .then(() => {
          dispatch(fetchRecentPheResults());
          handleFetchGraphData(dateRange.fromDate, dateRange.toDate); // Corrected
        })
        .catch((error) => {
          console.error("Error updating lab entry:", error);
        });
      setStatus((prev) => ({
        ...prev,
        recentPhe: "success",
      }));
    } catch (error) {
      console.error("Error updating lab entry:", error);
      setStatus((prev) => ({
        ...prev,
        recentPhe: "error",
      }));
    } finally {
      setIsPheResultFormVisible(false);
    }
  };

  const handleDelete = async (id: number) => {
    setStatus((prev) => ({
      ...prev,
      recentPhe: "loading",
    }));
    try {
      await dispatch(deleteLabResult(id))
        .unwrap()
        .then(() => {
          dispatch(fetchRecentPheResults());
          handleFetchGraphData(dateRange.fromDate, dateRange.toDate); // Corrected
        })
        .catch((error) => {
          console.error("Error deleting lab entry:", error);
        });
      setStatus((prev) => ({
        ...prev,
        recentPhe: "success",
      }));
    } catch (error: any) {
      console.error("Error deleting lab entry:", error);
      setStatus((prev) => ({
        ...prev,
        recentPhe: "error",
      }));
    } finally {
      setIsPheResultFormVisible(false);
    }
  };

  const isPheLoading =
    (status.recentPhe === "idle" && !lineData.length) ||
    status.recentPhe === "loading";
  const numOfSection =
    lineData.length > 4 && lineData.length < 7 ? lineData.length : 4;

  const dynamicSpacing = Math.min(
    ms(300),
    Math.max(ms(50), SCREEN_WIDTH / (lineData.length || 1))
  );
  const dynamicChartWidth = Math.max(
    SCREEN_WIDTH * 0.9,
    dynamicSpacing * lineData.length
  );

  return (
    <SafeScreen>
      <View style={styles.headerContainer}>
        <Header
          title="Lab Results"
          showHamburgerMenu
          isDark={variant === "dark"}
        />
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
        nestedScrollEnabled={true}
        scrollEnabled={true}
      >
        <View style={styles.sectionHeader}>
          <Button.Outline onPress={handleAddPress} style={styles.editButton}>
            <View style={styles.iconTextWrapper}>
              <Icons.Plus color={colors.textPrimary} />
              <Typography.B2 style={[Common.textBold, styles.textButton]}>
                Add
              </Typography.B2>
            </View>
          </Button.Outline>
        </View>

        <View style={styles.cardContainer}>
          {/* Chart Section */}
          <Card style={styles.chartWrapper}>
            <View style={styles.chartHeader}>
              <Typography.B2 style={[{ flex: 1 }, Common.textBold]}>
                Phe Level Trends
              </Typography.B2>

              <View style={styles.dropdownContainer}>
                <Dropdown
                  isVisible={dropdownVisible}
                  setSelectedValue={handleMetricChange}
                  selectedValue={metric}
                  data={dropdownData}
                  chevronColor={colors.white}
                  onToggle={() => setDropdownVisible(!dropdownVisible)}
                  dropdownStyle={styles.dropdownItemView}
                  dropdownOptionTextStyle={styles.dropdownOptionText}
                  buttonStyle={
                    !dropdownVisible
                      ? styles.dropdownViewClose
                      : styles.dropdownViewOpen
                  }
                  optionTextStyle={styles.optionTextStyle}

                />

                <Dropdown
                  isVisible={rangeDropdownVisible}
                  setSelectedValue={onRangeSelector}
                  selectedValue={selectedRange}
                  data={rangeDropDownData}
                  chevronColor={colors.white}
                  onToggle={() =>
                    setRangeDropdownVisible(!rangeDropdownVisible)
                  }
                  dropdownStyle={styles.dropdownItemView}
                  dropdownOptionTextStyle={styles.dropdownOptionText}
                  buttonStyle={
                    !rangeDropdownVisible
                      ? styles.rangeDropdownViewClose
                      : styles.rangeDropDownViewOpen
                  }
                  optionTextStyle={styles.optionTextStyle}
                />
              </View>
            </View>
            <Typography.B3 style={styles.chartRangeTitle}>
              {dateRange
                ? `${formatDateMMDDYYYY(dateRange?.fromDate)} to ${formatDateMMDDYYYY(dateRange.toDate)}`
                : ""}
            </Typography.B3>

            {/* Line Chart or No Data */}
            <View style={{ overflow: "hidden" }}></View>
            {lineData.length > 0 ? (
              <GScrollView
                showsHorizontalScrollIndicator={false}
                horizontal={true}
              >
                <LineChart
                  data={lineData}
                  lineGradient
                  height={ms(136)}
                  width={dynamicChartWidth} // Ensure full width for all data points
                  lineGradientId="chartGradient"
                  lineGradientComponent={() => (
                    <LinearGradient
                      id="chartGradient"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <Stop offset="1" stopColor={colors.tertiary} />
                      <Stop offset="0.5" stopColor={colors.secondary} />
                      <Stop offset="0" stopColor={colors.primary} />
                    </LinearGradient>
                  )}
                  dataPointsHeight={12}
                  dataPointsWidth={12}
                  dataPointsColor1={colors.primary}
                  pointerConfig={{
                    pointerStripColor: "transparent",
                    pointerStripWidth: 2,
                    pointerStripHeight: 0,
                    pointerStripUptoDataPoint: true,
                    activatePointersOnLongPress: false,
                    pointerColor: colors.primary,
                    pointerComponent: () => (
                      <View style={styles.pointerStyle} />
                    ),
                  }}
                  xAxisLabelTextStyle={styles.xAxisLabelTextStyle}
                  yAxisTextStyle={styles.yAxisTextStyle}
                  yAxisThickness={1}
                  xAxisThickness={1}
                  showFractionalValues={true}
                  roundToDigits={metric === "mg/dl" ? 1 : 2}
                  spacing={dynamicSpacing} // Adjust spacing dynamically
                  noOfSections={
                    metric !== "mg/dl" ? numOfSection * 2 : numOfSection
                  }
                  scrollAnimation
                  curved
                  dashGap={3}
                  dashWidth={1.5}
                  thickness={2}
                  curveType={CurveType.QUADRATIC}
                  yAxisColor={colors.transparent}
                  xAxisColor={colors.textPrimary}
                  // // hideDataPointsAtIndex={lineData
                  // //   .map((d, i) => (d.hideDataPoint ? i : -1))
                  // //   .filter((i) => i !== -1)}
                  // hidePointsAtZero
                />
              </GScrollView>
            ) : (status.graphTrends === "idle" && !lineData.length) ||
              status.graphTrends === "loading" ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
              </View>
            ) : (
              <Typography.B3 style={[Common.textBold, styles.noDataText]}>
                No results available for this date range
              </Typography.B3>
            )}
          </Card>
        </View>

        {/* Recent Phe Results */}
        <View style={styles.cardContainer}>
          <Card
            style={[
              styles.resultsContainer,
              { backgroundColor: colors.labsCard },
            ]}
          >
            <Typography.B3
              style={[
                Common.textBold,
                {
                  marginBottom: ms(10),
                  marginTop: ms(14),
                  color: colors.textPrimary,
                },
              ]}
            >
              Phe Results
            </Typography.B3>
            <GScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ flexGrow: 1 }}
              scrollEnabled
            >
              {isPheLoading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={colors.primary} />
                </View>
              ) : recentResults?.length ? (
                recentResults?.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handleResultPress(item)}
                  >
                    <View style={styles.resultItem}>
                      <View>
                        {item.pheMetrics === 1 ? (
                          <Typography.B1>
                            {item.pheMicromolePerLiter} μmol/l
                          </Typography.B1>
                        ) : (
                          <Typography.B1>
                            {item.pheMilligramPerDeciliter} mg/dl
                          </Typography.B1>
                        )}
                      </View>
                      <View style={styles.resultDateChevron}>
                        <Typography.B1>
                          {formatDateMMDDYYYY(item.sampleDate)}
                        </Typography.B1>
                        <Icons.ChevronIn
                          width={15}
                          height={15}
                          color={colors.textPrimary}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                ))
              ) : (
                <Typography.B3 style={[Common.textBold, styles.noDataText]}>
                  No results logged yet
                </Typography.B3>
              )}
            </GScrollView>
          </Card>
        </View>
      </ScrollView>
      <PheResultBottomSheet
        mode={mode}
        onSave={handleSubmit}
        onDelete={handleDelete}
        onUpdate={handleUpdate}
        isVisible={isPheResultFormVisible}
        selectedPheResult={selectedPheResult}
        onClose={() => setIsPheResultFormVisible(false)}
      />
      <SelectDateBottomSheet
        onSave={(selectedDateRange) => handleDateSave(selectedDateRange)}
        isVisible={isSelectDateVisible}
        onClose={() => setIsSelectDateVisible(false)}
      />
    </SafeScreen>
  );
};

export default LabScreen;
